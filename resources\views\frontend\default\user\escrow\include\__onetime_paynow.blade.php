<!-- kyc <PERSON> -->
<div class="modal fade" id="oneTimePayNowModal" tabindex="-1" aria-labelledby="oneTimePayNowModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-flex justify-content-between gap-1">
                <h1 class="modal-title fs-5" id="oneTimePayNowModalLabel">{{ __('Pay Now') }}</h1>
                <button type="button" class="modal-btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fi fi-br-circle-xmark"></i>
                </button>
            </div>
            <div class="modal-body">
                <form action="{{ route('user.escrow.onetime.payment', $escrow->id) }}" method="POST"
                    enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="amount" value="{{ $escrow->buyerPayableAmount() }}" class="pay-amount">
                    <div class="row gy-24">
                        <div class="col-lg-12">
                            <div class="td-form-group">
                                <label class="input-label">{{ __('Select Payment Method') }}<span>*</span></label>
                                <div class="input-field">
                                    <select class="defaultselect2 form-select payment_type" name="payment_type">
                                        <option value="">{{ __('Select Payment Method') }}</option>
                                        <option value="wallet">
                                            {{ __('Wallet') }}
                                            ({{ setting('currency_symbol') }}{{ number_format(auth()->user()->balance, 2) }})
                                        </option>
                                        <option value="direct">
                                            {{ __('Direct Payment') }}
                                        </option>
                                    </select>
                                </div>
                                @error('payment_type')
                                    <p class="feedback-invalid">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-12 direct_payment d-none">
                            <div class="td-form-group">
                                <label class="input-label">{{ __('Select Gateway') }}<span>*</span></label>
                                <div class="input-field">
                                    <select class="defaultselect2 form-select gateway" name="gateway_id">
                                        <option value="">{{ __('Select Gateway') }}</option>
                                        @foreach ($gateways as $gateway)
                                            <option value="{{ $gateway->id }}">
                                                {{ $gateway->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('gateway_id')
                                    <p class="feedback-invalid">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-12 direct_payment d-none">
                            <div class="put_gateway_info">

                            </div>
                        </div>
                    </div>
                    <div class="td-form-btns d-flex justify-content-end mt-30">
                        <button type="submit" class="td-btn btn-secondary">{{ __('Pay') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
