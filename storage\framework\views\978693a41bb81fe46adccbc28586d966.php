

<?php $__env->startSection('title'); ?>
    <?php echo e(__('Escrow Details')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('header_title'); ?>
    <?php echo e(__('Escrow Details')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row gy-30">
        <div class="col-xxl-12">
            <div class="my-escro-details">
                <div class="row gy-30">
                    <div class="col-xxl-5 col-xl-5 col-lg-6">
                        <!-- Escrow details -->
                        <div class="escrow-details-card">
                            <div class="escrow-details-heading">
                                <h6 class="title"><?php echo e(__('Details')); ?></h6>
                            </div>
                            <div class="escrow-details-inner">
                                <div class="escrow-details-list">
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Escrow ID')); ?></div>
                                        <div class="escrow-details-value">#<?php echo e($escrow->escrow_no); ?></div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Title')); ?></div>
                                        <div class="escrow-details-value"><?php echo e($escrow->title); ?></div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Transection')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php if($escrow->seller_id == auth()->id()): ?>
                                                <?php echo e(__('Selling to')); ?> <?php echo e($escrow->seller->full_name); ?>

                                            <?php else: ?>
                                                <?php echo e(__('Buying from')); ?> <?php echo e($escrow->buyer->full_name); ?>

                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Category')); ?></div>
                                        <div class="escrow-details-value"><?php echo e($escrow->category->name); ?></div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Amount')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php echo e($escrow->currency_sign); ?><?php echo e(number_format($escrow->amount, $escrow->decimal)); ?>

                                            <?php if($escrow->currency_id): ?>
                                                (<?php echo e(defaultAmountConversion($escrow->amount, $escrow->conversion_rate)); ?>)
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Charge')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php echo e($escrow->currency_sign); ?><?php echo e(number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true), $escrow->decimal)); ?>

                                            <?php if($escrow->currency_id): ?>
                                                (<?php echo e(setting('currency_symbol')); ?><?php echo e($escrow->charge); ?>)
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Charge Payer')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php if($escrow->charge_will_pay == 1): ?>
                                                <?php echo e(__('Buyer: ')); ?>

                                                <?php echo e($escrow->currency_sign); ?><?php echo e(number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true), $escrow->decimal)); ?>

                                                <?php if($escrow->currency_id): ?>
                                                    (<?php echo e(setting('currency_symbol')); ?>

                                                    <?php echo e(number_format(convertAmount($escrow->charge, $escrow->conversion_rate), $escrow->decimal)); ?>)
                                                <?php endif; ?>
                                            <?php elseif($escrow->charge_will_pay == 2): ?>
                                                <?php echo e(__('Seller: ')); ?>

                                                <?php echo e($escrow->currency_sign); ?><?php echo e(number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true), $escrow->decimal)); ?>

                                                <?php if($escrow->currency_id): ?>
                                                    (<?php echo e(setting('currency_symbol')); ?><?php echo e(number_format($escrow->charge, $escrow->decimal)); ?>)
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <?php echo e(__('Buyer: ')); ?>

                                                <?php echo e($escrow->currency_sign); ?><?php echo e(number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true) / 2, $escrow->decimal)); ?>

                                                <?php if($escrow->currency_id): ?>
                                                    (<?php echo e(setting('currency_symbol')); ?><?php echo e(number_format($escrow->charge / 2, $escrow->decimal)); ?>)
                                                <?php endif; ?>
                                                <br>
                                                <?php echo e(__('Seller: ')); ?>

                                                <?php echo e($escrow->currency_sign); ?><?php echo e(number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true) / 2, $escrow->decimal)); ?>

                                                <?php if($escrow->currency_id): ?>
                                                    (<?php echo e(setting('currency_symbol')); ?><?php echo e(number_format($escrow->charge / 2, $escrow->decimal)); ?>)
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Total Amount')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php echo e($escrow->currency_sign); ?><?php echo e(number_format($escrow->totalAmount(), $escrow->decimal)); ?>

                                            <?php if($escrow->currency_id): ?>
                                                (<?php echo e(setting('currency_symbol')); ?><?php echo e(number_format(convertAmount($escrow->totalAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global'))); ?>)
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Buyer Payable Amount')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php echo e($escrow->currency_sign); ?><?php echo e(number_format($escrow->buyerPayableAmount(), $escrow->decimal)); ?>

                                            <?php if($escrow->currency_id): ?>
                                                (<?php echo e(setting('currency_symbol')); ?><?php echo e(number_format(convertAmount($escrow->buyerPayableAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global'))); ?>)
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Seller Receivable Amount')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php echo e($escrow->currency_sign); ?><?php echo e(number_format($escrow->sellerReceivableAmount(), $escrow->decimal)); ?>

                                            <?php if($escrow->currency_id): ?>
                                                (<?php echo e(setting('currency_symbol')); ?><?php echo e(number_format(convertAmount($escrow->sellerReceivableAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global'))); ?>)
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Funded Amount')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php echo e($escrow->currency_sign); ?><?php echo e(number_format($escrow->fundedAmount(), $escrow->decimal)); ?>

                                            <?php if($escrow->currency_id): ?>
                                                (<?php echo e(setting('currency_symbol')); ?><?php echo e(number_format(convertAmount($escrow->fundedAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global'))); ?>)
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Remaining Amount')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php echo e($escrow->currency_sign); ?><?php echo e(number_format($escrow->remainingAmount(), $escrow->decimal)); ?>

                                            <?php if($escrow->currency_id): ?>
                                                (<?php echo e(setting('currency_symbol')); ?><?php echo e(number_format(convertAmount($escrow->remainingAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global'))); ?>)
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label"><?php echo e(__('Status')); ?></div>
                                        <div class="escrow-details-value">
                                            <?php if($escrow->status == \App\Enums\EscrowStatusEnum::Pending): ?>
                                                <span class="td-badge badge-warning"><?php echo e(__('Pending')); ?></span>
                                            <?php elseif($escrow->status == \App\Enums\EscrowStatusEnum::Approved): ?>
                                                <span class="td-badge badge-info"><?php echo e(__('Approved')); ?>

                                                <?php elseif($escrow->status == \App\Enums\EscrowStatusEnum::Released): ?>
                                                    <span class="td-badge badge-success"><?php echo e(__('Released')); ?>

                                                    <?php elseif($escrow->status == \App\Enums\EscrowStatusEnum::Disputed): ?>
                                                        <span class="td-badge badge-secondary"><?php echo e(__('Disputed')); ?>

                                                        <?php else: ?>
                                                            <span class="td-badge badge-danger"><?php echo e(__('Cancelled')); ?>

                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php if($escrow->status == \App\Enums\EscrowStatusEnum::Disputed): ?>
                                        <div class="escrow-details-item">
                                            <div class="escrow-details-label"><?php echo e(__('Disputed By')); ?></div>
                                            <div class="escrow-details-value">
                                                <?php if($escrow->disputed_by == 1): ?>
                                                    <?php echo e($escrow->seller->full_name); ?>

                                                <?php else: ?>
                                                    <?php echo e($escrow->buyer->full_name); ?>

                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="escrow-details-item">
                                            <div class="escrow-details-label"><?php echo e(__('Disputed Reason')); ?></div>
                                            <div class="escrow-details-value"><?php echo e($escrow->disputed_reason); ?></div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="btn-inner td-form-btns mt-30">

                                    <?php if($escrow->status == \App\Enums\EscrowStatusEnum::Pending): ?>
                                        <?php if(
                                            ($escrow->transaction_type == \App\Enums\EscrowTransactionType::Selling && $escrow->seller_id != $user->id) ||
                                                ($escrow->transaction_type == \App\Enums\EscrowTransactionType::Buying && $escrow->buyer_id != $user->id)): ?>
                                            <a href="javascript:void(0);"
                                                data-url="<?php echo e(route('user.escrow.status.update', [\App\Enums\EscrowStatusEnum::Approved])); ?>"
                                                data-confirm_message="<?php echo e(__('Are you sure you want to approve this escrow?')); ?>"
                                                class="td-btn btn-secondary btn-sm approve-escrow"><?php echo e(__('Approved Escrow')); ?></a>
                                        <?php endif; ?>
                                        <a href="javascript:void(0);"
                                            data-url="<?php echo e(route('user.escrow.status.update', [\App\Enums\EscrowStatusEnum::Cancelled])); ?>"
                                            data-confirm_message="<?php echo e(__('Are you sure you want to cancelled this escrow?')); ?>"
                                            class="td-btn danger-btn btn-sm cancelled-escrow"><?php echo e(__('Cancelled Escrow')); ?></a>
                                    <?php endif; ?>
                                    <?php if($escrow->status == \App\Enums\EscrowStatusEnum::Approved): ?>
                                        <a href="javascript:void(0);"
                                            class="td-btn danger-btn btn-sm disputed-escrow"><?php echo e(__('Dispute Escrow')); ?></a>
                                    <?php endif; ?>
                                    <?php if(
                                        $escrow->payment_option == 1 &&
                                            $escrow->status == \App\Enums\EscrowStatusEnum::Approved &&
                                            $user->id == $escrow->buyer_id): ?>
                                        <a href="javascript:void(0);" class="td-btn btn-secondary btn-sm"
                                            id="oneTimePayNow"><?php echo e(__('Pay Now')); ?></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-7 col-xl-7 col-lg-6">
                        <div class="escrow-details-card">
                            <div class="escrow-details-heading">
                                <h6 class="title"><?php echo e(__('Messages with')); ?>

                                    <span><?php echo e($escrow->seller_id == $user->id ? 'Buyer' : 'Seller'); ?></span>
                                </h6>
                            </div>
                            <div class="support-wrapper-box">
                                <div class="support-box-inner">
                                    <?php $__currentLoopData = $escrow->chats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <!-- User Message -->
                                        <div
                                            class="support-chat-text-item <?php if($chat->seller_id == $user->id || $chat->buyer_id == $user->id): ?> user-message <?php endif; ?>">
                                            <?php if($chat->buyer_id): ?>
                                                <div class="chat-text-avatar">
                                                    <div class="thumb">
                                                        <img src="<?php echo e(asset($chat->buyer->avatar_path)); ?>"
                                                            alt="<?php echo e($chat->buyer->full_name); ?>">
                                                    </div>
                                                    <?php if($chat->seller_id == $user->id || $chat->buyer_id == $user->id): ?>
                                                        <div class="contents">
                                                            <h6 class="title"><?php echo e($chat->buyer->full_name); ?></h6>
                                                            <span class="designation"><?php echo e($chat->buyer->email); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else: ?>
                                                <div class="chat-text-avatar">
                                                    <div class="thumb">
                                                        <img src="<?php echo e(asset($chat->seller->avatar_path)); ?>"
                                                            alt="<?php echo e($chat->seller->full_name); ?>">
                                                    </div>
                                                    <?php if($chat->seller_id == $user->id || $chat->buyer_id == $user->id): ?>
                                                        <div class="contents">
                                                            <h6 class="title"><?php echo e($chat->seller->full_name); ?></h6>
                                                            <span class="designation"><?php echo e($chat->seller->email); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                            <div class="message-list-box">
                                                <div class="message-list">
                                                    <p class="description"><?php echo e($chat->message); ?>

                                                    </p>
                                                    <span class="timestamp"><?php echo e($chat->created_at->diffForHumans()); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <!-- support input box -->
                                <div class="support-input-box">
                                    <form action="<?php echo e(route('user.escrow.chat.store')); ?>" method="POST"
                                        enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="escrow_id" value="<?php echo e(encrypt($escrow->id)); ?>">
                                        <textarea class="form-control" name="message" placeholder="Enter your message"></textarea>
                                        <div class="btn-inner d-flex justify-content-end">
                                            <button type="submit" class="td-btn mt-30">
                                                <span class="btn-text"><?php echo e(__('Send Message')); ?></span>
                                                <span class="btn-icon">
                                                    <svg width="16" height="14" viewBox="0 0 16 14"
                                                        fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M7.07513 7.0005H3.17881M3.11884 7.20701L1.46472 12.1481C1.33477 12.5363 1.2698 12.7303 1.31643 12.8499C1.35692 12.9537 1.44389 13.0324 1.5512 13.0623C1.67478 13.0968 1.86142 13.0128 2.23472 12.8448L14.0735 7.51737C14.4379 7.35337 14.6201 7.2714 14.6764 7.15749C14.7254 7.05859 14.7254 6.94248 14.6764 6.84351C14.6201 6.72967 14.4379 6.64763 14.0735 6.4837L2.23059 1.1544C1.85842 0.98692 1.67234 0.903184 1.54889 0.937535C1.44168 0.967367 1.35471 1.04585 1.31408 1.14945C1.26729 1.26875 1.33157 1.46242 1.46013 1.84975L3.1193 6.84861C3.14138 6.91513 3.15242 6.94836 3.15678 6.98236C3.16065 7.01261 3.16061 7.04315 3.15666 7.07333C3.15222 7.10733 3.14109 7.14055 3.11884 7.20701Z"
                                                            stroke="white" stroke-width="1.2" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                    </svg>
                                                </span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if($escrow->payment_option == 2): ?>
                        <div class="col-xxl-12">
                            <div class="default-table-wrapper-area">
                                <div class="card-heading">
                                    <h6 class="title"><?php echo e(__('Milestones')); ?></h6>
                                    <?php if($escrow->status == \App\Enums\EscrowStatusEnum::Approved && $user->id == $escrow->buyer_id): ?>
                                        <div class="link-inner">
                                            <a class="td-btn btn-h-36" href="javascript:void(0);" data-bs-toggle="modal"
                                                data-bs-target="#addMilestoneModal">
                                                <span class="btn-icon"> <i class="fi fi-br-plus"></i></span>
                                                <span class="btn-text"><?php echo e(__('Add New Milestone')); ?></span>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <!-- Recent history table -->
                                <div class="default-table-wrapper table-responsive">
                                    <table class="td-table">
                                        <thead>
                                            <tr>
                                                <th><?php echo e(__('Title')); ?></th>
                                                <th><?php echo e(__('Description')); ?></th>
                                                <th><?php echo e(__('Amount')); ?></th>
                                                <th><?php echo e(__('Status')); ?></th>
                                                <?php if($escrow->status == \App\Enums\EscrowStatusEnum::Approved && $user->id == $escrow->buyer_id): ?>
                                                    <th><?php echo e(__('Action')); ?></th>
                                                <?php endif; ?>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__empty_1 = true; $__currentLoopData = $escrow->milestones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <tr>
                                                    <td>
                                                        <span class="fw-7"><?php echo e($milestone->title); ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="fw-7"><?php echo e($milestone->description); ?></span>
                                                    </td>
                                                    <td>
                                                        <span
                                                            class="fw-7"><?php echo e($escrow->currency_sign); ?><?php echo e(number_format($milestone->amount, setting('site_currency_decimals', 'global'))); ?></span>
                                                    </td>
                                                    <td>
                                                        <?php if($milestone->status == \App\Enums\MilestoneStatusEnum::Unpaid): ?>
                                                            <span
                                                                class="td-badge badge-warning"><?php echo e(__('Unpaid')); ?></span>
                                                        <?php elseif($milestone->status == \App\Enums\MilestoneStatusEnum::Paid): ?>
                                                            <span class="td-badge badge-success"><?php echo e(__('Paid')); ?>

                                                            <?php else: ?>
                                                                <span class="td-badge badge-danger"><?php echo e(__('Disputed')); ?>

                                                        <?php endif; ?>
                                                    </td>
                                                    <?php if(
                                                        $escrow->status == \App\Enums\EscrowStatusEnum::Approved &&
                                                            $user->id == $escrow->buyer_id &&
                                                            $milestone->status == \App\Enums\MilestoneStatusEnum::Unpaid): ?>
                                                        <td>
                                                            <a href="javascript:void(0)"
                                                                class="td-btn btn-gray btn-h-36 pay-now"
                                                                data-milestone_id="<?php echo e($milestone->id); ?>"
                                                                data-amount="<?php echo e($milestone->amount); ?>">
                                                                <i class="icon-eye"></i>
                                                                <?php echo e(__('Pay')); ?>

                                                            </a>
                                                        </td>
                                                    <?php endif; ?>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <tr>
                                                    <td colspan="9" class="text-center">
                                                        <?php echo e(__('No milestones found')); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                            <!-- Add more rows as necessary -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    
    <?php echo $__env->make('frontend::user.escrow.include.__add_new_milestone_modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('frontend::user.escrow.include.__confirm_modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('frontend::user.escrow.include.__pay_now_modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('frontend::user.escrow.include.__onetime_paynow', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('frontend::user.escrow.include.__disputed_modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).on('change', 'input[type="file"]', function(event) {
            console.log($(this).next('label'));
            var $file = $(this),
                $label = $file.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            var fileName = $file.val().split('\\').pop(),
                file = event.target.files[0],
                fileType = file ? file.type.split('/')[0] : null,
                tmppath = file ? URL.createObjectURL(file) : null;

            if (fileName) {
                if (fileType === "image") {
                    $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
                } else {
                    $label.addClass('file-ok').css('background-image', 'none');
                }
                $labelText.text(fileName);
                $typeFileText.hide();
                $label.siblings('.file-upload-close').show();
            } else {
                resetUpload($file, $label, $labelText, $typeFileText,
                    labelDefault);
            }
        });

        $(document).on('click', '.file-upload-close', function() {
            var $button = $(this),
                $uploadWrapper = $button.closest('.upload-custom-file'),
                $fileInput = $uploadWrapper.find('input[type="file"]'),
                $label = $fileInput.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
        });

        function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
            $fileInput.val('');
            $label.removeClass('file-ok').css('background-image', 'none');
            $labelText.text(labelDefault);
            $typeFileText.show();
            $label.siblings('.file-upload-close').hide();
        }

        $(document).ready(function() {
            $('.support-box-inner').animate({
                scrollTop: $('.support-box-inner')[0].scrollHeight
            }, 500);

            $('.approve-escrow, .cancelled-escrow').on('click', function() {
                $('#confirmModal').modal('show');
                $('#confirmForm').attr('action', $(this).data('url'));
                $('.confirm-text').text($(this).data('confirm_message'));
            });

            $('.pay-now').on('click', function() {
                $('#payNowModal').modal('show');
                $('#payAmount').val($(this).data('amount'));
                $('#milestoneId').val($(this).data('milestone_id'));
            });

            $('#oneTimePayNow').on('click', function() {
                $('#oneTimePayNowModal').modal('show');
            });

            $('.disputed-escrow').on('click', function() {
                $('#disputedModal').modal('show');
            });

            $('.payment_type').on('change', function() {
                if ($(this).val() == 'direct') {
                    $('.direct_payment').removeClass('d-none');

                } else {
                    $('.direct_payment').addClass('d-none');
                }
            });

            if ($('.support-box-inner').height() > 434) {
                $('.support-box-inner').addClass('has-scrollbar');
            }

            $(".gateway").on('change', function() {
                let gateway_id = $(this).val();
                let that = $(this);
                $.ajax({
                    url: "<?php echo e(route('user.escrow.gateway')); ?>",
                    type: "POST",
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>",
                        gateway_id: gateway_id,
                        amount: that.parents('form').find('.pay-amount').val(),
                        escrow_id: '<?php echo e($escrow->id); ?>',
                    },
                    success: function(data) {
                        if (data) {
                            $('.put_gateway_info').html(data);
                        }
                    }
                })
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\escrow\app\Providers/../../resources/views/frontend/default/user/escrow/show.blade.php ENDPATH**/ ?>