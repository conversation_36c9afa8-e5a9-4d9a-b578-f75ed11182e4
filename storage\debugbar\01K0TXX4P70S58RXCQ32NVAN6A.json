{"__meta": {"id": "01K0TXX4P70S58RXCQ32NVAN6A", "datetime": "2025-07-23 12:00:35", "utime": **********.783911, "method": "POST", "uri": "/user/escrow/gateway", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.498891, "end": **********.783929, "duration": 0.2850379943847656, "duration_str": "285ms", "measures": [{"label": "Booting", "start": **********.498891, "relative_start": 0, "end": **********.714696, "relative_end": **********.714696, "duration": 0.****************, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.714704, "relative_start": 0.*****************, "end": **********.783931, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "69.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.726612, "relative_start": 0.****************, "end": **********.728587, "relative_end": **********.728587, "duration": 0.001974821090698242, "duration_str": "1.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend::user.escrow.include.__gateway", "start": **********.778819, "relative_start": 0.*****************, "end": **********.778819, "relative_end": **********.778819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.781091, "relative_start": 0.****************, "end": **********.782497, "relative_end": **********.782497, "duration": 0.001405954360961914, "duration_str": "1.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "escrow.test/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "frontend::user.escrow.include.__gateway", "param_count": null, "params": [], "start": **********.778782, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/user/escrow/include/__gateway.blade.phpfrontend::user.escrow.include.__gateway", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2Fescrow%2Finclude%2F__gateway.blade.php:1", "ajax": false, "filename": "__gateway.blade.php", "line": "?"}}]}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00391, "accumulated_duration_str": "3.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '72WHtxKS6xk5VK1IETvhXaMh9N3HSJTz73z7GnmQ' limit 1", "type": "query", "params": [], "bindings": ["72WHtxKS6xk5VK1IETvhXaMh9N3HSJTz73z7GnmQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.735514, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "escrow", "explain": null, "start_percent": 0, "width_percent": 13.811}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.744636, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "escrow", "explain": null, "start_percent": 13.811, "width_percent": 11.765}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.7477288, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "helpers.php:371", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2Fhelpers.php:371", "ajax": false, "filename": "helpers.php", "line": "371"}, "connection": "escrow", "explain": null, "start_percent": 25.575, "width_percent": 5.882}, {"sql": "select * from `deposit_methods` where `deposit_methods`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 344}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.7602031, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:344", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 344}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:344", "ajax": false, "filename": "UserEscrowController.php", "line": "344"}, "connection": "escrow", "explain": null, "start_percent": 31.458, "width_percent": 31.202}, {"sql": "select * from `escrows` where `escrows`.`id` = '40' limit 1", "type": "query", "params": [], "bindings": ["40"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 345}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.763174, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:345", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 345}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:345", "ajax": false, "filename": "UserEscrowController.php", "line": "345"}, "connection": "escrow", "explain": null, "start_percent": 62.66, "width_percent": 7.161}, {"sql": "select * from `currencies` where `currencies`.`id` = 4 and `currencies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 147}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 348}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.767428, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Escrow.php:147", "source": {"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 147}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FEscrow.php:147", "ajax": false, "filename": "Escrow.php", "line": "147"}, "connection": "escrow", "explain": null, "start_percent": 69.821, "width_percent": 22.251}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\escrow\\app\\Providers\\ViewServiceProvider.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.777313, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:60", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\escrow\\app\\Providers\\ViewServiceProvider.php", "line": 60}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FProviders%2FViewServiceProvider.php:60", "ajax": false, "filename": "ViewServiceProvider.php", "line": "60"}, "connection": "escrow", "explain": null, "start_percent": 92.072, "width_percent": 7.928}]}, "models": {"data": {"App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\DepositMethod": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FDepositMethod.php:1", "ajax": false, "filename": "DepositMethod.php", "line": "?"}}, "App\\Models\\Escrow": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FEscrow.php:1", "ajax": false, "filename": "Escrow.php", "line": "?"}}, "App\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://escrow.test/user/escrow/gateway", "action_name": "user.escrow.gateway", "controller_action": "App\\Http\\Controllers\\Frontend\\UserEscrowController@gateway", "uri": "POST user/escrow/gateway", "controller": "App\\Http\\Controllers\\Frontend\\UserEscrowController@gateway<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:342\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:342\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserEscrowController.php:342-360</a>", "middleware": "web, auth:web, 2fa, check_deactivation, verified", "duration": "286ms", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1450286007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1450286007\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-40082732 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7S36T45hWPABgUhCs1m1p8LgmqV6H52HR3V1attG</span>\"\n  \"<span class=sf-dump-key>gateway_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>escrow_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">40</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40082732\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1254915976 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"736 characters\">cookieConsent=accepted; XSRF-TOKEN=eyJpdiI6Ii9oL2tQY3doazNLRitNL1htVUJnZGc9PSIsInZhbHVlIjoiMlRHajNqRDNPM2hWZ2pDSFEyUnZXK2NJVEJZeURiSlloWWNCaVV2SVlIQTNHZzl2a3VONUlnNFQrL0R5b2hEVjJYZlpZOGgxMktDUzE2dFQvdG42c3NXMG4zRFJLSno2dE5EdjBPd0pnQWZQYWJKRm94NTFiRFYxRGRMMGRaeS8iLCJtYWMiOiIyZWFjOWYwMmIyNjRkNjM0MDMzOWE4ZDI1YWQ0OWNjYzg4NmUyMGY5MjhhYWI3MWRmOGU3ZGZiNjZlYzY0YWU0IiwidGFnIjoiIn0%3D; escrow_session=eyJpdiI6IjdQcGovbHU3RUY3VlYrZXRZWC9vZWc9PSIsInZhbHVlIjoiTE41UytSNmtXSmVOUHlwWjJ0OVRpY3FoNlh4N1J3ZFM3RkNxRjgzNWs2QkNHM0ZEWFNsbFBtazF5T2ZmdFN1Z2F1LzlwOFllZGdpRFo1ZW9UQTNNSU9qaDNUT0xObmxnUkw0WTFEL1d4NjZiT2x4YktTL2pBazhhaHVLalZ6OW4iLCJtYWMiOiJjNWJjNGZjMDUxMGU1YjA1NTkzMjE0MDkyM2QwYWI4MjRmMTZmM2UwNWJiZGFjMjJkNGU3OWFkOWVlMTE3YzUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"236 characters\">http://escrow.test/user/escrow/show/eyJpdiI6IkRuTGFNNTlKRHBoaUJPdGhiVTRBeVE9PSIsInZhbHVlIjoiMGYwaFp1WVowTDBiUS9rNWhpYWFYZz09IiwibWFjIjoiM2Y4ODA0YWM1OTUxZmM5OWI3ODBjNzdjYmI2MTE4ODJjMGFjMTI3YWY2MmE2M2MxOTY2OTYyN2IyNzI0OWU2NyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">http://escrow.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">85</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">escrow.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254915976\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1567013856 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookieConsent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7S36T45hWPABgUhCs1m1p8LgmqV6H52HR3V1attG</span>\"\n  \"<span class=sf-dump-key>escrow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">72WHtxKS6xk5VK1IETvhXaMh9N3HSJTz73z7GnmQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567013856\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-421243025 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 06:00:35 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421243025\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1475095181 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7S36T45hWPABgUhCs1m1p8LgmqV6H52HR3V1attG</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"236 characters\">http://escrow.test/user/escrow/show/eyJpdiI6IkRuTGFNNTlKRHBoaUJPdGhiVTRBeVE9PSIsInZhbHVlIjoiMGYwaFp1WVowTDBiUS9rNWhpYWFYZz09IiwibWFjIjoiM2Y4ODA0YWM1OTUxZmM5OWI3ODBjNzdjYmI2MTE4ODJjMGFjMTI3YWY2MmE2M2MxOTY2OTYyN2IyNzI0OWU2NyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>deposit_tnx</span>\" => \"<span class=sf-dump-str title=\"13 characters\">TRX4I9YBOFPS6</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475095181\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://escrow.test/user/escrow/gateway", "action_name": "user.escrow.gateway", "controller_action": "App\\Http\\Controllers\\Frontend\\UserEscrowController@gateway"}, "badge": null}}