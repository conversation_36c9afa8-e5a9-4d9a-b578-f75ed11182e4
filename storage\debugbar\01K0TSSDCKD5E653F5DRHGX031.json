{"__meta": {"id": "01K0TSSDCKD5E653F5DRHGX031", "datetime": "2025-07-23 10:48:39", "utime": **********.316226, "method": "GET", "uri": "/user/notification-read/183", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.050109, "end": **********.316239, "duration": 0.2661302089691162, "duration_str": "266ms", "measures": [{"label": "Booting", "start": **********.050109, "relative_start": 0, "end": **********.265704, "relative_end": **********.265704, "duration": 0.*****************, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.265712, "relative_start": 0.*****************, "end": **********.316241, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "50.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.277698, "relative_start": 0.*****************, "end": **********.279708, "relative_end": **********.279708, "duration": 0.002009868621826172, "duration_str": "2.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.314607, "relative_start": 0.****************, "end": **********.314892, "relative_end": **********.314892, "duration": 0.00028514862060546875, "duration_str": "285μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "escrow.test/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00604, "accumulated_duration_str": "6.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO' limit 1", "type": "query", "params": [], "bindings": ["g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.2838762, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "escrow", "explain": null, "start_percent": 0, "width_percent": 7.285}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.290771, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "escrow", "explain": null, "start_percent": 7.285, "width_percent": 7.285}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.293209, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "helpers.php:371", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2Fhelpers.php:371", "ajax": false, "filename": "helpers.php", "line": "371"}, "connection": "escrow", "explain": null, "start_percent": 14.57, "width_percent": 4.47}, {"sql": "select * from `notifications` where `user_id` = 1 and `notifications`.`id` = '183' and `notifications`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, "183"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.304619, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "UserController.php:42", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 42}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php:42", "ajax": false, "filename": "UserController.php", "line": "42"}, "connection": "escrow", "explain": null, "start_percent": 19.04, "width_percent": 8.609}, {"sql": "update `notifications` set `read` = 1, `notifications`.`updated_at` = '2025-07-23 10:48:39' where `id` = 183", "type": "query", "params": [], "bindings": [1, "2025-07-23 10:48:39", 183], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 46}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.306385, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "UserController.php:46", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/UserController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserController.php", "line": 46}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php:46", "ajax": false, "filename": "UserController.php", "line": "46"}, "connection": "escrow", "explain": null, "start_percent": 27.649, "width_percent": 72.351}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Notification": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://escrow.test/user/notification-read/183", "action_name": "user.read-notification", "controller_action": "App\\Http\\Controllers\\Frontend\\UserController@readNotification", "uri": "GET user/notification-read/{id}", "controller": "App\\Http\\Controllers\\Frontend\\UserController@readNotification<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserController.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserController.php:30-50</a>", "middleware": "web, auth:web, 2fa, check_deactivation, verified", "duration": "267ms", "peak_memory": "32MB", "response": "Redirect to http://escrow.test/user/escrow/show/eyJpdiI6InNjaVJKQkp4WkZSNjFBdHNUcGdWTVE9PSIsInZhbHVlIjoienlyU0xwd3JZRk5FRGJmYjNoL2hIZz09IiwibWFjIjoiOWUyMjY0ZmY4ZmI4MWJiMDRjYjMyNDNjNWRkYzI3NTlmZjhhMGZjMmUxMDlkYjM2MDJjNTYzOGU5ZTEwMTRhMiIsInRhZyI6IiJ9", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-134238580 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-134238580\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-888742749 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-888742749\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1241355457 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"736 characters\">cookieConsent=accepted; XSRF-TOKEN=eyJpdiI6ImNsK0FvVWRYR2UwdjJQMzlOTlR0Q1E9PSIsInZhbHVlIjoicFV5TnJNREVuQTFIZ0g5YmtHRTE1ZWVkSG1HaDdERlhMcUtoMkpUTTM0eW94SXJ4R3NXU3BiUjVrYXVWNkpKUkFnR1c1RDBwYVFxV3MvVGZaWmpId0c3dDJXVHFzRmZTb1hhY0JHc2doRGxZUEFWdGpjdjN2bjc2SVoxb2lQUkkiLCJtYWMiOiJhYTM2YWE4YTY3YWJiMTYxOTM2NDE4NmY3M2M5ZWZmN2MxMGY4NWE5ZTAzMzlmNDk0MTYzODk2MDE0NWM3ZGExIiwidGFnIjoiIn0%3D; escrow_session=eyJpdiI6Ilh1RUhJMDVQa0hrZks4UHJqUjgvTkE9PSIsInZhbHVlIjoiQ3diSU5pVENJUHZsOWNhelczSHdBb3ZHbWkwaHh3WjJ0d3ptRkFraWFjcWNsWFJlYjNrTFBOZ1pXNWQ1RDd3YThjRk9JUlZReVdCc1NhVnZBT2NrZkE3RTFSRjRILzRMSTgvd3ZtNVY0bXV1ZWJiMkVxKzUvYlNTbXdkeUhpem8iLCJtYWMiOiI2MzZhZmQ2YjYzYzAwYmRhNjJjOTRlZDA3ZjFjMjY1OGM0MGQ5ZWY4MDgxYzAxZjA4ODEzMGU1ZTFlNWFiNzkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://escrow.test/user/escrows</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">escrow.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1241355457\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1999424583 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookieConsent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x3KUpIYUikkGzQ1LnzqNpY2JzMKTtkxMI3UCODwc</span>\"\n  \"<span class=sf-dump-key>escrow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999424583\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2118748486 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 04:48:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"236 characters\">http://escrow.test/user/escrow/show/eyJpdiI6InNjaVJKQkp4WkZSNjFBdHNUcGdWTVE9PSIsInZhbHVlIjoienlyU0xwd3JZRk5FRGJmYjNoL2hIZz09IiwibWFjIjoiOWUyMjY0ZmY4ZmI4MWJiMDRjYjMyNDNjNWRkYzI3NTlmZjhhMGZjMmUxMDlkYjM2MDJjNTYzOGU5ZTEwMTRhMiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2118748486\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-16636564 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x3KUpIYUikkGzQ1LnzqNpY2JzMKTtkxMI3UCODwc</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://escrow.test/user/escrows</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>deposit_tnx</span>\" => \"<span class=sf-dump-str title=\"13 characters\">TRX4I9YBOFPS6</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16636564\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://escrow.test/user/notification-read/183", "action_name": "user.read-notification", "controller_action": "App\\Http\\Controllers\\Frontend\\UserController@readNotification"}, "badge": "302 Found"}}