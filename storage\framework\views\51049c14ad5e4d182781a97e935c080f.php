<?php $__env->startSection('title'); ?>
    <?php echo e(__('Withdraw Money')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('header_title'); ?>
    <?php echo e(__('Withdraw Money')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row gy-30">
        <div class="col-xxl-12">
            <!-- Withdraw money area start -->
            <div class="withdraw-money-area">
                <?php echo $__env->make('frontend::user.withdraw._topbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <div class="row gy-30">
                    <div class="col-xxl-5 col-xl-5 col-lg-6">
                        <div class="progress-step-wrapper">
                            <div class="progress-wrapper">
                                <div class="step-item inactive first step_item_one">
                                    <div class="step-number number">
                                        01
                                    </div>
                                    <div class="step-number icon d-none">
                                        <i class="fi fi-br-check"></i>
                                    </div>
                                    <div class="step-content">
                                        <div class="step-title"><?php echo e(__('Step 1')); ?></div>
                                        <div class="step-description"><?php echo e(__('Amount')); ?></div>
                                    </div>
                                    <div class="step-line"></div>
                                </div>

                                <div class="step-item inactive step_item_two">
                                    <div class="step-number">02</div>
                                    <div class="step-content">
                                        <div class="step-title"><?php echo e(__('Step 2')); ?></div>
                                        <div class="step-description"><?php echo e(__('Details & Review')); ?></div>
                                    </div>
                                    <div class="step-line"></div>
                                </div>

                                <div class="step-item inactive">
                                    <div class="step-number">03</div>
                                    <div class="step-content">
                                        <div class="step-title"><?php echo e(__('Step 3')); ?></div>
                                        <div class="step-description"><?php echo e(__('Confirm & Success')); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-7 col-xl-7 col-lg-6">
                        <!-- My escrow form -->
                        <div class="default">
                            <form action="<?php echo e(route('user.withdrawMoney.now')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <div class="amount_step">
                                    <div class="row gy-24">
                                        <div class="col-lg-12">
                                            <div class="td-form-group">
                                                <label class="input-label"><?php echo e(__('Getaway')); ?><span>*</span></label>
                                                <div class="input-field">
                                                    <select class="defaultselect2 form-select" name="withdraw_account"
                                                        id="paymentGateway">
                                                        <option disabled selected>
                                                            <?php echo e(__('Select Withdraw Account')); ?>

                                                        </option>

                                                        <?php $__currentLoopData = $withdrawAccounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $withdrawAccount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($withdrawAccount->id); ?>"
                                                                data-type="<?php echo e($withdrawAccount->method->type); ?>"
                                                                data-code="<?php echo e($withdrawAccount->method->gateway_code); ?>"
                                                                data-maximum-amount="<?php echo e($withdrawAccount->method->max_withdraw); ?>"
                                                                data-minimum-amount="<?php echo e($withdrawAccount->method->min_withdraw); ?>"
                                                                data-field-option="<?php echo e(json_encode($withdrawAccount->method->field_options)); ?>"
                                                                data-charge="<?php echo e($withdrawAccount->method->charge); ?>"
                                                                data-charge_type="<?php echo e($withdrawAccount->method->charge_type == 'percentage' ? '%' : $withdrawAccount->currency); ?>"
                                                                data-charge_type_text="<?php echo e($withdrawAccount->method->charge_type); ?>"
                                                                data-currency="<?php echo e($withdrawAccount->method->currency); ?>">
                                                                <?php echo e($withdrawAccount->method_name); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                                <p class="feedback-invalid gateway_msg"></p>

                                                <small class="feedback-invalid text-danger gateway_text"></small>
                                            </div>
                                        </div>
                                        <div class="col-lg-12">
                                            <div class="td-form-group">
                                                <label class="input-label" for="amount"><?php echo e(__('Amount')); ?>

                                                    <span>*</span>
                                                </label>
                                                <div class="input-field">
                                                    <input type="text" class="form-control" id="amount" name="amount"
                                                        required min="1">
                                                </div>
                                                <p class="feedback-invalid amount_msg"></p>
                                                <span class="feedback-attention danger-text amount_text"></span>
                                            </div>
                                        </div>

                                        <div class="put_manual_field">

                                        </div>

                                        <div class="td-form-btns d-flex justify-content-end mt-30">
                                            <a href="javascript:void(0);"
                                                class="td-btn btn-secondary reviewStep"><?php echo e(__('Next Step')); ?></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="review_step d-none">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Withdraw money area end -->
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        (function($) {
            'use strict';

            $(document).on('change', 'input[type="file"]', function(event) {
                var $file = $(this),
                    $label = $file.next('label'),
                    $labelText = $label.find('span:first'),
                    $typeFileText = $label.find('.type-file-text'),
                    labelDefault = "Upload Image";

                var fileName = $file.val().split('\\').pop(),
                    file = event.target.files[0],
                    fileType = file ? file.type.split('/')[0] : null,
                    tmppath = file ? URL.createObjectURL(file) : null;

                if (fileName) {
                    if (fileType === "image") {

                        $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
                    } else {

                        $label.addClass('file-ok').css('background-image', 'none');
                    }
                    $labelText.text(fileName);
                    $typeFileText.hide();
                    $label.siblings('.file-upload-close').show();
                } else {
                    resetUpload($file, $label, $labelText, $typeFileText,
                        labelDefault);
                }
            });

            $(document).on('click', '.file-upload-close', function() {
                var $button = $(this),
                    $uploadWrapper = $button.closest('.upload-custom-file'),
                    $fileInput = $uploadWrapper.find('input[type="file"]'),
                    $label = $fileInput.next('label'),
                    $labelText = $label.find('span:first'),
                    $typeFileText = $label.find('.type-file-text'),
                    labelDefault = "Upload Image";

                resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
            });

            $(document).on('change', '#paymentGateway', function() {
                let gatewayType = $(this).find(':selected').data('type');
                let gatewayCode = $(this).find(':selected').data('code');
                let charge = $(this).find(':selected').data('charge');
                let charge_type = $(this).find(':selected').data('charge_type');
                let minimum_amount = $(this).find(':selected').data('minimum-amount');
                let maximum_amount = $(this).find(':selected').data('maximum-amount');
                let currency = $(this).find(':selected').data('currency');

                $('.gateway_text').text('Charge ' + Number(charge).toFixed(2) + ' ' + charge_type);
                $('.amount_text').text('Minimum ' + Number(minimum_amount).toFixed(2) + ' ' + currency +
                    ' and Maximum ' + Number(maximum_amount).toFixed(2) + ' ' + currency);

                if (gatewayType === 'manual') {
                    $.ajax({
                        url: '<?php echo e(route('user.withdrawMoney.manual.payment')); ?>',
                        type: 'POST',
                        data: {
                            _token: '<?php echo e(csrf_token()); ?>',
                            code: gatewayCode,
                        },
                        success: function(data) {
                            if (data) {
                                $('.put_manual_field').html(data);

                            }
                        }
                    });
                } else {
                    $('.put_manual_field').html('');
                }
            });

            $(document).on('click', '.back_to_amount', function() {
                $('.amount_step').removeClass('d-none');
                $('.review_step').addClass('d-none');
                $('.step_item_one').removeClass('completed');
                $('.step_item_one').addClass('inactive');
                $('.step_item_one').find('.number').removeClass('d-none');
                $('.step_item_one').find('.icon').addClass('d-none');
                $('.step_item_two').removeClass('completed');
                $('.step_item_two').addClass('inactive');
                $('.step_item_two').find('.number').removeClass('d-none');
                $('.step_item_two').find('.icon').addClass('d-none');
            });

            $(document).on('click', '.reviewStep', function() {
                let gateway = $('#paymentGateway').val();
                let amount = $('#amount').val();
                let max_amount = $('#paymentGateway').find(':selected').data('maximum-amount');
                let min_amount = $('#paymentGateway').find(':selected').data('minimum-amount');
                let field_option = $('#paymentGateway').find(':selected').data('field-option');
                let isValid = true;

                if (!gateway) {
                    $('.gateway_msg').html(<?php echo json_encode(__('Please select a gateway.'), 15, 512) ?>);
                    isValid = false;
                }

                if (!amount) {
                    $('.amount_msg').html(<?php echo json_encode(__('Please enter an amount.'), 15, 512) ?>);
                    isValid = false;
                }

                if (gateway && amount) {
                    if (parseFloat(amount) < parseFloat(min_amount)) {
                        $('.amount_msg').html(<?php echo json_encode(__('Amount is less than minimum amount.'), 15, 512) ?>);
                        isValid = false;
                    } else if (parseFloat(amount) > parseFloat(max_amount)) {
                        $('.amount_msg').html(<?php echo json_encode(__('Amount is greater than maximum amount.'), 15, 512) ?>);
                        isValid = false;
                    } else {
                        $('.amount_msg').html('');
                        $('.gateway_msg').html('');
                    }
                }

                if (field_option) {
                    if (Object.values(field_option).length > 0) {
                        Object.values(field_option).forEach(function(field) {
                            if (field.validation === 'required' && $('#' + field.name.replace(/\s+/g,
                                    '_')).val() === '') {
                                $('.' + field.name.replace(/\s+/g, '_') + '_msg').text(
                                    <?php echo json_encode(__('This field is required.'), 15, 512) ?>);
                                isValid = false;
                            } else {
                                $('.' + field.name.replace(/\s+/g, '_') + '_msg').text('');
                            }
                        });
                    }
                }

                if (isValid) {
                    let amount = $('#amount').val();
                    let currency = $('#paymentGateway').find(':selected').data('currency');
                    let currency_symbol = $('#paymentGateway').find(':selected').data('currency-symbol');
                    let charge = $('#paymentGateway').find(':selected').data('charge');
                    let charge_type_text = $('#paymentGateway').find(':selected').data('charge_type_text');

                    let final_charge = 0;
                    if (charge_type_text == 'percentage') {
                        final_charge += charge / 100 * amount;
                    } else {
                        final_charge += charge;
                    }

                    let total_amount = parseFloat(amount) + parseFloat(final_charge);

                    let html = `<div class="default-form-card">
                        <div class="add-list-table table-responsive">
                            <table class="td-info-table">
                                <tbody>
                                    <tr>
                                        <td>${<?php echo json_encode(__('Amount'), 15, 512) ?>}</td>
                                        <td class="text-right">${Number(amount).toFixed(2)} ${currency}</td>
                                    </tr>
                                    <tr class="charge">
                                        <td>${<?php echo json_encode(__('Charge'), 15, 512) ?>}</td>
                                        <td class="text-right text-danger">${Number(final_charge).toFixed(2)} ${currency}</td>
                                    </tr>
                                    <tr>
                                        <td>${<?php echo json_encode(__('Payable Amount'), 15, 512) ?>}</td>
                                        <td class="text-right">${Number(total_amount).toFixed(2)} ${currency}</td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr class="total-row">
                                        <td>${<?php echo json_encode(__('Total Amount'), 15, 512) ?>}</td>
                                        <td class="text-right total-price">${Number(total_amount).toFixed(2)} ${currency}
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        <div class="td-form-btns justify-content-end mt-30">
                            <a href="void:javascript(0);" class="td-btn btn-gray back_to_amount">
                                <span class="btn-icon">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g clip-path="url(#clip0_167_2081)">
                                            <path
                                                d="M15.1006 6.74552L15.1256 6.75098H4.41913L7.78485 3.37784C7.94966 3.21316 8.04007 2.99007 8.04007 2.75592C8.04007 2.52178 7.94966 2.30025 7.78485 2.13518L7.26114 1.61121C7.09646 1.44653 6.87701 1.35547 6.643 1.35547C6.40885 1.35547 6.18927 1.44588 6.02459 1.61056L0.255093 7.37953C0.0897596 7.54487 -0.000646921 7.76509 3.48506e-06 7.99937C-0.000646921 8.23495 0.0897596 8.45531 0.255093 8.62038L6.02459 14.3899C6.18927 14.5544 6.40872 14.645 6.643 14.645C6.87701 14.645 7.09646 14.5543 7.26114 14.3899L7.78485 13.8659C7.94966 13.7015 8.04007 13.4819 8.04007 13.2478C8.04007 13.0137 7.94966 12.8057 7.78485 12.6412L4.38114 9.24919H15.1126C15.5948 9.24919 16 8.83358 16 8.35163V7.61056C16 7.12861 15.5828 6.74552 15.1006 6.74552Z"
                                                fill="#002F34" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_167_2081">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                </span>
                                <span class="btn-icon">${<?php echo json_encode(__('Back'), 15, 512) ?>}</span>
                            </a>
                            <button type="submit" class="td-btn btn-secondary">
                                <span class="btn-text">${<?php echo json_encode(__('Confirm'), 15, 512) ?>}</span>
                            </button>
                        </div>
                    </div>`;

                    $('.amount_step').addClass('d-none');
                    $('.review_step').removeClass('d-none');
                    $('.review_step').html(html);
                    $('.step_item_one').addClass('completed');
                    $('.step_item_one').removeClass('inactive');
                    $('.step_item_one').find('.number').addClass('d-none');
                    $('.step_item_one').find('.icon').removeClass('d-none');
                    $('.step_item_two').addClass('completed');
                    $('.step_item_two').removeClass('inactive');
                }

            });

            function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
                $fileInput.val('');
                $label.removeClass('file-ok').css('background-image', 'none');
                $labelText.text(labelDefault);
                $typeFileText.show();
                $label.siblings('.file-upload-close').hide();
            }

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>



<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\escrow\app\Providers/../../resources/views/frontend/default/user/withdraw/withdraw_money.blade.php ENDPATH**/ ?>