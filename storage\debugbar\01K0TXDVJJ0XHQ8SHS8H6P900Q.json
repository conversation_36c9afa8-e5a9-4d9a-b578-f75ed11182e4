{"__meta": {"id": "01K0TXDVJJ0XHQ8SHS8H6P900Q", "datetime": "2025-07-23 11:52:14", "utime": **********.931085, "method": "GET", "uri": "/user/escrows", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.566354, "end": **********.9311, "duration": 0.3647458553314209, "duration_str": "365ms", "measures": [{"label": "Booting", "start": **********.566354, "relative_start": 0, "end": **********.781703, "relative_end": **********.781703, "duration": 0.****************, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.781712, "relative_start": 0.*****************, "end": **********.931101, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.794675, "relative_start": 0.*****************, "end": **********.796526, "relative_end": **********.796526, "duration": 0.0018508434295654297, "duration_str": "1.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.835918, "relative_start": 0.****************, "end": **********.92966, "relative_end": **********.92966, "duration": 0.*****************, "duration_str": "93.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend::user.escrow.index", "start": **********.846148, "relative_start": 0.*****************, "end": **********.846148, "relative_end": **********.846148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._pagination", "start": **********.862391, "relative_start": 0.***************, "end": **********.862391, "relative_end": **********.862391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.user", "start": **********.871075, "relative_start": 0.3047208786010742, "end": **********.871075, "relative_end": **********.871075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::include._notify", "start": **********.889643, "relative_start": 0.3232889175415039, "end": **********.889643, "relative_end": **********.889643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.user_header", "start": **********.897094, "relative_start": 0.33073997497558594, "end": **********.897094, "relative_end": **********.897094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.user_sidebar", "start": **********.921623, "relative_start": 0.3552689552307129, "end": **********.921623, "relative_end": **********.921623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 33843136, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "escrow.test/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "frontend::user.escrow.index", "param_count": null, "params": [], "start": **********.846111, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/user/escrow/index.blade.phpfrontend::user.escrow.index", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2Fescrow%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "frontend::user._include._pagination", "param_count": null, "params": [], "start": **********.862359, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/user/_include/_pagination.blade.phpfrontend::user._include._pagination", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_pagination.blade.php:1", "ajax": false, "filename": "_pagination.blade.php", "line": "?"}}, {"name": "frontend::layouts.user", "param_count": null, "params": [], "start": **********.871018, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user.blade.phpfrontend::layouts.user", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser.blade.php:1", "ajax": false, "filename": "user.blade.php", "line": "?"}}, {"name": "frontend::include._notify", "param_count": null, "params": [], "start": **********.889608, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/include/_notify.blade.phpfrontend::include._notify", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Finclude%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}}, {"name": "frontend::layouts.user_header", "param_count": null, "params": [], "start": **********.897063, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.phpfrontend::layouts.user_header", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_header.blade.php:1", "ajax": false, "filename": "user_header.blade.php", "line": "?"}}, {"name": "frontend::layouts.user_sidebar", "param_count": null, "params": [], "start": **********.921588, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_sidebar.blade.phpfrontend::layouts.user_sidebar", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_sidebar.blade.php:1", "ajax": false, "filename": "user_sidebar.blade.php", "line": "?"}}]}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.007149999999999999, "accumulated_duration_str": "7.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'a0a9NEsYrUse7MuYBggUWoWGtSzg20pEnHyByHW1' limit 1", "type": "query", "params": [], "bindings": ["a0a9NEsYrUse7MuYBggUWoWGtSzg20pEnHyByHW1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.801629, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "escrow", "explain": null, "start_percent": 0, "width_percent": 9.79}, {"sql": "delete from `sessions` where `last_activity` <= 1753242734", "type": "query", "params": [], "bindings": [1753242734], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 280}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 177}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 118}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 63}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.804581, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:280", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 280}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:280", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "280"}, "connection": "escrow", "explain": null, "start_percent": 9.79, "width_percent": 5.315}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.810941, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "escrow", "explain": null, "start_percent": 15.105, "width_percent": 5.734}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.813427, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "helpers.php:371", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2Fhelpers.php:371", "ajax": false, "filename": "helpers.php", "line": "371"}, "connection": "escrow", "explain": null, "start_percent": 20.839, "width_percent": 3.077}, {"sql": "select * from `categories` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 40}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.8245409, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:40", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 40}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:40", "ajax": false, "filename": "UserEscrowController.php", "line": "40"}, "connection": "escrow", "explain": null, "start_percent": 23.916, "width_percent": 7.972}, {"sql": "select count(*) as aggregate from `escrows` where (`buyer_id` = 2 or `seller_id` = 2)", "type": "query", "params": [], "bindings": [2, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.826678, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 31.888, "width_percent": 5.035}, {"sql": "select * from `escrows` where (`buyer_id` = 2 or `seller_id` = 2) order by `created_at` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [2, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.827886, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 36.923, "width_percent": 4.056}, {"sql": "select * from `categories` where `categories`.`id` in (4, 5)", "type": "query", "params": [], "bindings": [4, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.831217, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 40.979, "width_percent": 3.776}, {"sql": "select * from `users` where `users`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.832369, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 44.755, "width_percent": 4.056}, {"sql": "select * from `users` where `users`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.8335109, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 48.811, "width_percent": 3.636}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\escrow\\app\\Providers\\ViewServiceProvider.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.8446722, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:60", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\escrow\\app\\Providers\\ViewServiceProvider.php", "line": 60}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FProviders%2FViewServiceProvider.php:60", "ajax": false, "filename": "ViewServiceProvider.php", "line": "60"}, "connection": "escrow", "explain": null, "start_percent": 52.448, "width_percent": 6.154}, {"sql": "select * from `currencies` where `currencies`.`id` = 4 and `currencies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 142}, {"index": 28, "namespace": "view", "name": "frontend::user.escrow.index", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/user/escrow/index.blade.php", "line": 106}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.850059, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Escrow.php:142", "source": {"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 142}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FEscrow.php:142", "ajax": false, "filename": "Escrow.php", "line": "142"}, "connection": "escrow", "explain": null, "start_percent": 58.601, "width_percent": 7.133}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 and `currencies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 142}, {"index": 28, "namespace": "view", "name": "frontend::user.escrow.index", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/user/escrow/index.blade.php", "line": 106}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.852859, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Escrow.php:142", "source": {"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 142}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FEscrow.php:142", "ajax": false, "filename": "Escrow.php", "line": "142"}, "connection": "escrow", "explain": null, "start_percent": 65.734, "width_percent": 4.895}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 393}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.8720021, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "helpers.php:393", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 393}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2Fhelpers.php:393", "ajax": false, "filename": "helpers.php", "line": "393"}, "connection": "escrow", "explain": null, "start_percent": 70.629, "width_percent": 9.371}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 2 and `notifications`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["user", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::layouts.user_header", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.898674, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "frontend::layouts.user_header:4", "source": {"index": 15, "namespace": "view", "name": "frontend::layouts.user_header", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 4}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_header.blade.php:4", "ajax": false, "filename": "user_header.blade.php", "line": "4"}, "connection": "escrow", "explain": null, "start_percent": 80, "width_percent": 9.231}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'user' and `user_id` = 2 and `read` = 0 and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["user", 2, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::layouts.user_header", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 5}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.900352, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "frontend::layouts.user_header:5", "source": {"index": 16, "namespace": "view", "name": "frontend::layouts.user_header", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 5}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_header.blade.php:5", "ajax": false, "filename": "user_header.blade.php", "line": "5"}, "connection": "escrow", "explain": null, "start_percent": 89.231, "width_percent": 4.755}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 401}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.906669, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "helpers.php:401", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 401}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2Fhelpers.php:401", "ajax": false, "filename": "helpers.php", "line": "401"}, "connection": "escrow", "explain": null, "start_percent": 93.986, "width_percent": 6.014}]}, "models": {"data": {"App\\Models\\Category": {"value": 8, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 5, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Language": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Escrow": {"value": 2, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FEscrow.php:1", "ajax": false, "filename": "Escrow.php", "line": "?"}}, "App\\Models\\Currency": {"value": 2, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "App\\Models\\Notification": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}}, "count": 22, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://escrow.test/user/escrows", "action_name": "user.escrow.index", "controller_action": "App\\Http\\Controllers\\Frontend\\UserEscrowController@index", "uri": "GET user/escrows", "controller": "App\\Http\\Controllers\\Frontend\\UserEscrowController@index<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:37\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:37\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserEscrowController.php:37-59</a>", "middleware": "web, auth:web, 2fa, check_deactivation, verified", "duration": "366ms", "peak_memory": "34MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1167149589 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1167149589\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-165823286 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-165823286\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-319370708 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"736 characters\">cookieConsent=accepted; XSRF-TOKEN=eyJpdiI6IjdtYXhvRTFXcEYwdXFqRjNaUi80Z2c9PSIsInZhbHVlIjoic1BDYis5WWs0T0R4MG01OUk4bmRlb0hxbEMydUJMOHZNdnpYRTNWbTAvVDZIdGU3NmxTUmlZWDdGMStWOGRtWHVuWHlIcTJzaGZyOWJNODgrLzc5VVl3Y2J5RUwvRnlUY2NkRDdaTUxpQXV6MmdTMkU4K21NbWJwRDRzVUk2ZzEiLCJtYWMiOiI1NWM2NTNiOTA2NjAxYjg0MGJmZDllYzNmZWJjNzQwNmZjYjE3OTMxNzY3ZjgwYjRhZmJlY2YyZjRjMjdmY2Y4IiwidGFnIjoiIn0%3D; escrow_session=eyJpdiI6IlUraHZmVCtUeTFuNlc2VUZaZWswRWc9PSIsInZhbHVlIjoiODFOUXh2bUYwWDByTkZsTmJxbmpwWkphTS8rd2FLMnlpc3hNZW9FelMvdlNUTW94NWpnQW9nUzhqdGRmb2xPaUc2V0xqSEtiaXgrYXl3OU9IY3FsampTYUplSElUTnk0RGNBOGNxUlhvRmtvSk9UWEpYUTlueHBJbGkwSVBjUUIiLCJtYWMiOiI2ZGYwYjQwODNhMjQzMDdlY2M0Y2YzNGI2YmM0ZGFhNTI3YWUyNjE3N2VmM2Y2NDRjMjcyZmI5MzVjMWYwMzE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://escrow.test/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">escrow.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319370708\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-836956920 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookieConsent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S4lpC2qY65tzt3qwUiIwQXoo7D146MP6azJ462xT</span>\"\n  \"<span class=sf-dump-key>escrow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a0a9NEsYrUse7MuYBggUWoWGtSzg20pEnHyByHW1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836956920\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1739192665 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 05:52:14 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739192665\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1194433054 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S4lpC2qY65tzt3qwUiIwQXoo7D146MP6azJ462xT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"236 characters\">http://escrow.test/user/escrow/show/eyJpdiI6InBJdjU0NWxtaFZ3NThSR1ZSbG9tY1E9PSIsInZhbHVlIjoiRkg1R0NMYmVkU0dnMVZPNlZVMkpZUT09IiwibWFjIjoiZDU0MTM5NDY2NzM4YzMwNGY3M2E2MTg0OGJlNjk1MDk1ZWQ4NTIzNzk1MWYyZDMxMGY1ZWM2NDIwYzVkMjU0MiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>deposit_tnx</span>\" => \"<span class=sf-dump-str title=\"13 characters\">TRX4I9YBOFPS6</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194433054\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://escrow.test/user/escrows", "action_name": "user.escrow.index", "controller_action": "App\\Http\\Controllers\\Frontend\\UserEscrowController@index"}, "badge": null}}