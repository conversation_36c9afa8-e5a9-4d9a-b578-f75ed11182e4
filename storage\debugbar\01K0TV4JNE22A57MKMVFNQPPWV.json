{"__meta": {"id": "01K0TV4JNE22A57MKMVFNQPPWV", "datetime": "2025-07-23 11:12:13", "utime": **********.742679, "method": "GET", "uri": "/user/escrows", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.330452, "end": **********.742691, "duration": 0.41223907470703125, "duration_str": "412ms", "measures": [{"label": "Booting", "start": **********.330452, "relative_start": 0, "end": **********.588527, "relative_end": **********.588527, "duration": 0.****************, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.588535, "relative_start": 0.****************, "end": **********.742693, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.604983, "relative_start": 0.*****************, "end": **********.607462, "relative_end": **********.607462, "duration": 0.0024788379669189453, "duration_str": "2.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.653333, "relative_start": 0.*****************, "end": **********.740922, "relative_end": **********.740922, "duration": 0.*****************, "duration_str": "87.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend::user.escrow.index", "start": **********.663941, "relative_start": 0.*****************, "end": **********.663941, "relative_end": **********.663941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._pagination", "start": **********.680784, "relative_start": 0.*************5684, "end": **********.680784, "relative_end": **********.680784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.user", "start": **********.688032, "relative_start": 0.35757994651794434, "end": **********.688032, "relative_end": **********.688032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::include._notify", "start": **********.703309, "relative_start": 0.37285709381103516, "end": **********.703309, "relative_end": **********.703309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.user_header", "start": **********.710288, "relative_start": 0.3798360824584961, "end": **********.710288, "relative_end": **********.710288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.user_sidebar", "start": **********.733332, "relative_start": 0.4028799533843994, "end": **********.733332, "relative_end": **********.733332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 33873824, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "escrow.test/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "frontend::user.escrow.index", "param_count": null, "params": [], "start": **********.663903, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/user/escrow/index.blade.phpfrontend::user.escrow.index", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2Fescrow%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "frontend::user._include._pagination", "param_count": null, "params": [], "start": **********.68075, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/user/_include/_pagination.blade.phpfrontend::user._include._pagination", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_pagination.blade.php:1", "ajax": false, "filename": "_pagination.blade.php", "line": "?"}}, {"name": "frontend::layouts.user", "param_count": null, "params": [], "start": **********.688001, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user.blade.phpfrontend::layouts.user", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser.blade.php:1", "ajax": false, "filename": "user.blade.php", "line": "?"}}, {"name": "frontend::include._notify", "param_count": null, "params": [], "start": **********.703266, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/include/_notify.blade.phpfrontend::include._notify", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Finclude%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}}, {"name": "frontend::layouts.user_header", "param_count": null, "params": [], "start": **********.710258, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.phpfrontend::layouts.user_header", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_header.blade.php:1", "ajax": false, "filename": "user_header.blade.php", "line": "?"}}, {"name": "frontend::layouts.user_sidebar", "param_count": null, "params": [], "start": **********.7333, "type": "blade", "hash": "bladeC:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_sidebar.blade.phpfrontend::layouts.user_sidebar", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_sidebar.blade.php:1", "ajax": false, "filename": "user_sidebar.blade.php", "line": "?"}}]}, "queries": {"count": 16, "nb_statements": 16, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00829, "accumulated_duration_str": "8.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO' limit 1", "type": "query", "params": [], "bindings": ["g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.614547, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "escrow", "explain": null, "start_percent": 0, "width_percent": 6.514}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.624589, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "escrow", "explain": null, "start_percent": 6.514, "width_percent": 11.098}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.6284401, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "helpers.php:371", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2Fhelpers.php:371", "ajax": false, "filename": "helpers.php", "line": "371"}, "connection": "escrow", "explain": null, "start_percent": 17.612, "width_percent": 5.669}, {"sql": "select * from `categories` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 40}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.6401808, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:40", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 40}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:40", "ajax": false, "filename": "UserEscrowController.php", "line": "40"}, "connection": "escrow", "explain": null, "start_percent": 23.281, "width_percent": 11.098}, {"sql": "select count(*) as aggregate from `escrows` where (`buyer_id` = 1 or `seller_id` = 1)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.643095, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 34.379, "width_percent": 7.72}, {"sql": "select * from `escrows` where (`buyer_id` = 1 or `seller_id` = 1) order by `created_at` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.64466, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 42.099, "width_percent": 3.981}, {"sql": "select * from `categories` where `categories`.`id` in (4, 5)", "type": "query", "params": [], "bindings": [4, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.648177, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 46.08, "width_percent": 4.343}, {"sql": "select * from `users` where `users`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.649488, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 50.422, "width_percent": 4.946}, {"sql": "select * from `users` where `users`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.650856, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "UserEscrowController.php:56", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserEscrowController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Frontend\\UserEscrowController.php", "line": 56}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:56", "ajax": false, "filename": "UserEscrowController.php", "line": "56"}, "connection": "escrow", "explain": null, "start_percent": 55.368, "width_percent": 3.378}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\escrow\\app\\Providers\\ViewServiceProvider.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.662044, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:60", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\escrow\\app\\Providers\\ViewServiceProvider.php", "line": 60}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FProviders%2FViewServiceProvider.php:60", "ajax": false, "filename": "ViewServiceProvider.php", "line": "60"}, "connection": "escrow", "explain": null, "start_percent": 58.745, "width_percent": 5.308}, {"sql": "select * from `currencies` where `currencies`.`id` = 4 and `currencies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 142}, {"index": 28, "namespace": "view", "name": "frontend::user.escrow.index", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/user/escrow/index.blade.php", "line": 106}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.668405, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Escrow.php:142", "source": {"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 142}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FEscrow.php:142", "ajax": false, "filename": "Escrow.php", "line": "142"}, "connection": "escrow", "explain": null, "start_percent": 64.053, "width_percent": 9.65}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 and `currencies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 142}, {"index": 28, "namespace": "view", "name": "frontend::user.escrow.index", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/user/escrow/index.blade.php", "line": 106}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.6713681, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Escrow.php:142", "source": {"index": 22, "namespace": null, "name": "app/Models/Escrow.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\Escrow.php", "line": 142}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FEscrow.php:142", "ajax": false, "filename": "Escrow.php", "line": "142"}, "connection": "escrow", "explain": null, "start_percent": 73.703, "width_percent": 3.498}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 393}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.688493, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "helpers.php:393", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 393}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2Fhelpers.php:393", "ajax": false, "filename": "helpers.php", "line": "393"}, "connection": "escrow", "explain": null, "start_percent": 77.201, "width_percent": 4.946}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 1 and `notifications`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["user", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::layouts.user_header", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.711886, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "frontend::layouts.user_header:4", "source": {"index": 15, "namespace": "view", "name": "frontend::layouts.user_header", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 4}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_header.blade.php:4", "ajax": false, "filename": "user_header.blade.php", "line": "4"}, "connection": "escrow", "explain": null, "start_percent": 82.147, "width_percent": 10.977}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'user' and `user_id` = 1 and `read` = 0 and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["user", 1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::layouts.user_header", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 5}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.713905, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "frontend::layouts.user_header:5", "source": {"index": 16, "namespace": "view", "name": "frontend::layouts.user_header", "file": "C:\\laragon\\www\\escrow\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 5}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_header.blade.php:5", "ajax": false, "filename": "user_header.blade.php", "line": "5"}, "connection": "escrow", "explain": null, "start_percent": 93.124, "width_percent": 4.343}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 401}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7185628, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "helpers.php:401", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 401}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2Fhelpers.php:401", "ajax": false, "filename": "helpers.php", "line": "401"}, "connection": "escrow", "explain": null, "start_percent": 97.467, "width_percent": 2.533}]}, "models": {"data": {"App\\Models\\Notification": {"value": 12, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\Category": {"value": 8, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 5, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Language": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Escrow": {"value": 2, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FEscrow.php:1", "ajax": false, "filename": "Escrow.php", "line": "?"}}, "App\\Models\\Currency": {"value": 2, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 33, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://escrow.test/user/escrows", "action_name": "user.escrow.index", "controller_action": "App\\Http\\Controllers\\Frontend\\UserEscrowController@index", "uri": "GET user/escrows", "controller": "App\\Http\\Controllers\\Frontend\\UserEscrowController@index<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:37\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserEscrowController.php:37\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserEscrowController.php:37-59</a>", "middleware": "web, auth:web, 2fa, check_deactivation, verified", "duration": "414ms", "peak_memory": "34MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-883238875 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-883238875\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1792997914 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1792997914\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-928663085 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"736 characters\">cookieConsent=accepted; XSRF-TOKEN=eyJpdiI6IjZJYnhaVGJlUEliUDNjMGpmUXo1Q0E9PSIsInZhbHVlIjoidWNpQ1ZDemdnajJWOE9Ia1hrckNDamQvL1VpdkFMbVNrSUE0UWViNnJzMFFtc2gzWDNpTGxuQXEyMEVMRnBOcTIvbEQ3UkU0b1llcWxBVmZySTV4elVZR2w4Mmhtc3dESGF2ODFnd0xXWWx5aTY2MGNQaXlBL3BUc0ZqVitOcSsiLCJtYWMiOiJkZmU0YzFiZDM3ODFlY2Y0ODc4NTljNDYzNDlkNDdjMmQ4MGZkZmIwMDlhOWI1MzI2MzNiYmMyZjExZTM1N2E2IiwidGFnIjoiIn0%3D; escrow_session=eyJpdiI6IldQUi80ZzJ0RXpiTGx0M2lXQWdacFE9PSIsInZhbHVlIjoidEFoRFZoei9ORTR0cndkZUdwR3FjbWxPVjlLRGxsNllUWDU2eDhWL01EeitjemVoNTR5bTBTTHNkREgrTXZoQ0JQK1YxYUFwL3NmNzZqeG9Ob1dCblF1NVJXdEIxcExaTGJrOFFWM2RudGxIM2hXSEV0YkNad0N4SnUrR0hPbmMiLCJtYWMiOiI0NGEyNjcxOTQxYjYxYzY5NDU0OGRhODY4ZGJmMDYyYzRhZDI0MTZjMzJmYmI3NWFjZTA3NzZkZTQ2MTQzNjI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://escrow.test/user/escrows</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">escrow.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928663085\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-691458223 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookieConsent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x3KUpIYUikkGzQ1LnzqNpY2JzMKTtkxMI3UCODwc</span>\"\n  \"<span class=sf-dump-key>escrow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691458223\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-657224003 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 05:12:13 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-657224003\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1599398839 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x3KUpIYUikkGzQ1LnzqNpY2JzMKTtkxMI3UCODwc</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://escrow.test/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>deposit_tnx</span>\" => \"<span class=sf-dump-str title=\"13 characters\">TRX4I9YBOFPS6</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599398839\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://escrow.test/user/escrows", "action_name": "user.escrow.index", "controller_action": "App\\Http\\Controllers\\Frontend\\UserEscrowController@index"}, "badge": null}}