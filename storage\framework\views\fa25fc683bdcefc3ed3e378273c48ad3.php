<form action="<?php echo e(route('user.setting.update')); ?>" method="post" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <?php echo method_field('PUT'); ?>
    <input type="hidden" name="type" value="personal">
    <div class="row gy-30">
        <div class="col-xxl-3 col-xl-4 col-lg-4">
            <div class="setting-left-side">
                <div class="setting-user-wrapper">
                    <div class="setting-user-info">
                        <div class="user-thumb">
                            <img src="<?php echo e(asset($data->avatar_path)); ?>" alt="User Profile">
                        </div>
                        <div class="user-info">
                            <h5 class="name"><?php echo e($data->full_name); ?></h5>
                            <span class="designation"><?php echo e($data->email); ?></span>
                        </div>
                    </div>
                    <div class="upload-option">
                        <button type="button"
                            onclick="document.getElementById('getFile').click()"><?php echo e(__('Upload new picture')); ?></button>
                        <input type='file' name="avatar" id="getFile" style="display:none">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xxl-9 col-xl-8 col-lg-8">
            <!-- setting form -->
            <div class="default-form-card">
                <div class="row gy-24">
                    <!-- First Name -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="f-name"><?php echo e(__('First Name')); ?><span>*</span></label>
                            <div class="input-field">
                                <input type="text" class="form-control" id="f-name" name="first_name"
                                    value="<?php echo e(old('first_name', $data->first_name)); ?>" required>
                            </div>
                            <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Last Name -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="l-name"><?php echo e(__('Last Name')); ?><span>*</span></label>
                            <div class="input-field">
                                <input type="text" class="form-control" id="l-name" name="last_name"
                                    value="<?php echo e(old('last_name', $data->last_name)); ?>" required>
                            </div>
                            <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Username -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="u-name"><?php echo e(__('Username')); ?><span>*</span></label>
                            <div class="input-field">
                                <input type="text" class="form-control" id="u-name" name="username"
                                    value="<?php echo e(old('username', $data->username)); ?>" required>
                            </div>
                            <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Gender -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="gender"><?php echo e(__('Gender')); ?><span>*</span></label>
                            <div class="input-field">
                                <select class="defaultselect2 form-select" id="gender" name="gender" required>
                                    <option value="male" <?php if($data->gender == 'male'): ?> selected <?php endif; ?>>
                                        <?php echo e(__('Male')); ?>

                                    </option>
                                    <option value="female" <?php if($data->gender == 'female'): ?> selected <?php endif; ?>>
                                        <?php echo e(__('Female')); ?>

                                    </option>
                                </select>
                            </div>
                            <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Date of Birth -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="d-birth"><?php echo e(__('Date of Birth')); ?><span>*</span></label>
                            <div class="input-field">
                                <input type="date" class="form-control" id="d-birth" name="date_of_birth"
                                    value="<?php echo e(old('date_of_birth', $data->date_of_birth)); ?>" required>
                            </div>
                            <?php $__errorArgs = ['date_of_birth'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="u-email"><?php echo e(__('Email Address')); ?><span>*</span></label>
                            <div class="input-field">
                                <input type="email" class="form-control" id="u-email" name="email"
                                    value="<?php echo e(old('email', $data->email)); ?>" required>
                            </div>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Country -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="country"><?php echo e(__('Country')); ?><span>*</span></label>
                            <div class="input-field">
                                <select class="form-select select2Icons form-control" id="country" name="country"
                                    required>
                                    <option value=""><?php echo e(__('Select Country')); ?></option>
                                    <?php $__currentLoopData = getCountries(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($country['name']); ?>" <?php if($country['name'] == old('country', $data->country)): echo 'selected'; endif; ?>>
                                            <?php echo e($country['name']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Phone -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="phone"><?php echo e(__('Phone')); ?><span>*</span></label>
                            <div class="input-field">
                                <input type="tel" class="form-control" id="phone" name="phone"
                                    value="<?php echo e(old('phone', $data->phone)); ?>" required>
                            </div>
                            <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- City -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="city"><?php echo e(__('City')); ?><span>*</span></label>
                            <div class="input-field">
                                <input type="text" class="form-control" id="city" name="city"
                                    value="<?php echo e(old('city', $data->city)); ?>" required>
                            </div>
                            <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Zip Code -->
                    <div class="col-lg-6">
                        <div class="td-form-group">
                            <label class="input-label" for="zip-code"><?php echo e(__('Zip Code')); ?><span>*</span></label>
                            <div class="input-field">
                                <input type="text" class="form-control" id="zip-code" name="zip"
                                    value="<?php echo e(old('zip', $data->zip_code)); ?>" required>
                            </div>
                            <?php $__errorArgs = ['zip'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="col-lg-12">
                        <div class="td-form-group">
                            <label class="input-label" for="address"><?php echo e(__('Address')); ?><span>*</span></label>
                            <div class="input-field">
                                <textarea class="form-control" name="address" id="address"><?php echo e(old('address', $data->address)); ?></textarea>
                            </div>
                            <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
                <div class="td-form-btns d-flex justify-content-end mt-30">
                    <button type="submit" class="td-btn btn-secondary"><?php echo e(__('Update Information')); ?></button>
                </div>
            </div>
        </div>
    </div>
</form>

<?php /**PATH C:\laragon\www\escrow\app\Providers/../../resources/views/frontend/default/user/settings/include/__personal.blade.php ENDPATH**/ ?>