<!-- Pages navigation -->
<div class="pages-navigation-area mb-30">
    <div class="pages-navigation">
        <div class="nav">
            <ul>
                <li class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                    'active' => request('type') == 'personal' || !request('type'),
                ]); ?>">
                    <a href="<?php echo e(route('user.setting.index')); ?>?type=personal">
                        <?php echo e(__('Account Settings')); ?>

                    </a>
                </li>
                <li class="<?php echo \Illuminate\Support\Arr::toCssClasses(['active' => request('type') == 'password']); ?>">
                    <a href="<?php echo e(route('user.setting.index')); ?>?type=password">
                        <?php echo e(__('Change Password')); ?>

                    </a>
                </li>
                <?php if(setting('kyc_verification', 'permission')): ?>
                    <li class="<?php echo \Illuminate\Support\Arr::toCssClasses(['active' => request('type') == 'kyc']); ?>">
                        <a href="<?php echo e(route('user.setting.index')); ?>?type=kyc">
                            <?php echo e(__('KYC')); ?>

                        </a>
                    </li>
                <?php endif; ?>
                <?php if(setting('fa_verification', 'permission')): ?>
                    <li class="<?php echo \Illuminate\Support\Arr::toCssClasses(['active' => request('type') == 'security']); ?>">
                        <a href="<?php echo e(route('user.setting.index')); ?>?type=security">
                            <?php echo e(__('Security')); ?>

                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\escrow\app\Providers/../../resources/views/frontend/default/user/settings/include/__setting_menu_list.blade.php ENDPATH**/ ?>