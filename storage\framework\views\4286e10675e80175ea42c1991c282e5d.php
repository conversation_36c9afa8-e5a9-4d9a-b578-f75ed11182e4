<div class="escrow-details-card mb-3">
    <div class="escrow-details-inner">
        <div class="escrow-details-list">
            <div class="escrow-details-item">
                <div class="escrow-details-label"><?php echo e(__('Amount:')); ?></div>
                <div class="escrow-details-value">
                    <?php echo e(formatAmountNumber($amount, $escrow->currency_type)); ?>

                    <?php echo e($currency_symbol); ?>

                    <?php if($escrow->currency_id): ?>
                        (<?php echo e($escrow->currency_sign); ?><?php echo e(number_format(convertAmount($amount, $escrow->conversion_rate, true), $escrow->decimal)); ?>)
                    <?php endif; ?>
                </div>
            </div>
            <div class="escrow-details-item">
                <div class="escrow-details-label"><?php echo e(__('Charge:')); ?></div>
                <div class="escrow-details-value">
                    <?php echo e(formatAmountNumber($charge, $escrow->currency_type)); ?>

                    <?php echo e($currency_symbol); ?>

                    <?php if($escrow->currency_id): ?>
                        (<?php echo e($escrow->currency_sign); ?><?php echo e(number_format(convertAmount($charge, $escrow->conversion_rate, true), $escrow->decimal)); ?>)
                    <?php endif; ?>
                </div>
            </div>
            <div class="escrow-details-item">
                <div class="escrow-details-label"><?php echo e(__('Payable Amount:')); ?></div>
                <div class="escrow-details-value">
                    <?php echo e(formatAmountNumber($payable_amount, $escrow->currency_type)); ?>

                    <?php echo e($currency_symbol); ?>

                    <?php if($escrow->currency_id): ?>
                        (<?php echo e($escrow->currency_sign); ?><?php echo e(number_format(convertAmount($payable_amount, $escrow->conversion_rate, true), $escrow->decimal)); ?>)
                    <?php endif; ?>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="row gy-24">
    <?php if($filed_options): ?>
        <div class="col-lg-12">
            <div class="alert alert-warning mt-3" role="alert">
                <?php echo $payment_details; ?>

            </div>
        </div>
        <?php $__currentLoopData = $filed_options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($option['type'] == 'file'): ?>
                <div class="col-lg-12">
                    <div class="td-form-group">
                        <label class="input-label"><?php echo e(ucfirst($option['name'])); ?>

                            <span><?php echo e($option['validation'] == 'required' ? '*' : ''); ?></span></label>
                        <div class="input-field">
                            <div id="uploadItems">
                                <div class="upload-custom-file">
                                    <input type="file"
                                        name="<?php echo e(strtolower(str_replace(' ', '_', $option['name']))); ?>"
                                        class="upload-input" accept=".gif, .jpg, .png, .jpeg, .webp, .pdf, .svg"
                                        id="image_two">
                                    <label for="image_two">
                                        <img class="upload-icon"
                                            src="<?php echo e(asset('frontend/default/images/icons/upload-iocn.svg')); ?>"
                                            alt="upload file">
                                        <span> <b><?php echo e(__('Upload')); ?> <?php echo e(ucfirst($option['name'])); ?></b></span>
                                    </label>
                                    <button type="button" class="file-upload-close" style="display: none;">
                                        <i><iconify-icon icon="tabler:circle-x"></iconify-icon></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php elseif($option['type'] == 'textarea'): ?>
                <div class="col-lg-12">
                    <div class="td-form-group">
                        <label class="input-label"
                            for="<?php echo e(strtolower(str_replace(' ', '_', $option['name']))); ?>"><?php echo e(ucfirst($option['name'])); ?>

                            <span><?php echo e($option['validation'] == 'required' ? '*' : ''); ?></span></label>
                        <div class="input-field">
                            <textarea class="form-control" name="<?php echo e(strtolower(str_replace(' ', '_', $option['name']))); ?>"
                                id="<?php echo e(strtolower(str_replace(' ', '_', $option['name']))); ?>" <?php echo e($option['validation']); ?>></textarea>
                        </div>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="feedback-invalid"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="col-lg-12">
                    <div class="td-form-group">
                        <label class="input-label"
                            for="title"><?php echo e(ucfirst($option['name'])); ?><span><?php echo e($option['validation'] == 'required' ? '*' : ''); ?></span></label>
                        <div class="input-field">
                            <input type="<?php echo e($option['type']); ?>" class="form-control" id="title"
                                name="<?php echo e(strtolower(str_replace(' ', '_', $option['name']))); ?>"
                                <?php echo e($option['validation']); ?>>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
</div>

<script>
    function fileUpload() {
        $(document).on('change', 'input[type="file"]', function(event) {
            console.log($(this).next('label'));
            var $file = $(this),
                $label = $file.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            var fileName = $file.val().split('\\').pop(),
                file = event.target.files[0],
                fileType = file ? file.type.split('/')[0] : null,
                tmppath = file ? URL.createObjectURL(file) : null;

            if (fileName) {
                if (fileType === "image") {
                    $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
                } else {
                    $label.addClass('file-ok').css('background-image', 'none');
                }
                $labelText.text(fileName);
                $typeFileText.hide();
                $label.siblings('.file-upload-close').show();
            } else {
                resetUpload($file, $label, $labelText, $typeFileText,
                    labelDefault);
            }
        });

        $(document).on('click', '.file-upload-close', function() {
            var $button = $(this),
                $uploadWrapper = $button.closest('.upload-custom-file'),
                $fileInput = $uploadWrapper.find('input[type="file"]'),
                $label = $fileInput.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
        });

        function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
            $fileInput.val('');
            $label.removeClass('file-ok').css('background-image', 'none');
            $labelText.text(labelDefault);
            $typeFileText.show();
            $label.siblings('.file-upload-close').hide();
        }
    }
</script><?php /**PATH C:\laragon\www\escrow\app\Providers/../../resources/views/frontend/default/user/escrow/include/__gateway.blade.php ENDPATH**/ ?>