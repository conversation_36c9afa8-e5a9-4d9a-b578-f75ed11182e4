<?php

namespace App\Http\Controllers\Frontend;

use App\Models\User;
use App\Enums\TxnType;
use App\Models\Escrow;
use App\Traits\Payment;
use App\Enums\TxnStatus;
use App\Facades\Txn\Txn;
use App\Models\Category;
use App\Models\Currency;
use App\Models\Milestone;
use App\Enums\GatewayType;
use App\Models\Transaction;
use App\Traits\ImageUpload;
use App\Traits\NotifyTrait;
use Illuminate\Http\Request;
use App\Enums\CurrencyStatus;
use App\Models\ChargeSetting;
use App\Models\DepositMethod;
use App\Enums\EscrowStatusEnum;
use App\Enums\MilestoneStatusEnum;
use Illuminate\Support\Facades\DB;
use App\Enums\EscrowTransactionType;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\ManualGatewayRequest;

class UserEscrowController extends Controller
{
    use ImageUpload;
    use NotifyTrait;
    use Payment;

    public function index(Request $request)
    {
        $userId = auth()->id();
        $categories = Category::where('status', 1)->get();
        $escrows = Escrow::with('category', 'buyer', 'seller')
            ->where(function ($query)  use ($userId) {
                $query->where('buyer_id', $userId)
                    ->orWhere('seller_id', $userId);
            })
            ->when($request->input('escrow_no'), function ($query) use ($request) {
                $query->where('escrow_no', $request->input('escrow_no'));
            })
            ->when($request->input('status'), function ($query) use ($request) {
                $status = EscrowStatusEnum::tryFrom($request->input('status'));
                if ($status) {
                    $query->where('status', $status);
                }
            })
            ->latest()
            ->paginate(20);

        return view('frontend::user.escrow.index', compact('escrows', 'categories'));
    }

    public function create()
    {
        $categories = Category::where('status', 1)->get();
        $currencies = Currency::where('status', CurrencyStatus::Active)->get();
        return view('frontend::user.escrow.create', compact('categories', 'currencies'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
            'type' => 'required',
            'category_id' => 'required|exists:categories,id',
            'amount' => 'required',
            'charge_payer' => 'required',
            'seller_email' => ($request->input('type') == 'buy' ? 'required' : 'nullable') . '|email|exists:users,email',
            'buyer_email' => ($request->input('type') == 'sell' ? 'required' : 'nullable') . '|email|exists:users,email',
            'currency_id' => setting('multiple_currency', 'permission') ? 'required' : 'nullable',
        ]);

        $email = $request->input('type') === 'buy' ? $request->input('seller_email') : $request->input('buyer_email');
        $userId = User::where('email', $email)->value('id');

        if ($userId == auth()->id()) {
            notify()->error(__('You can not create escrow with your own email.'));
            return redirect()->back();
        }

        $input = $request->except(['seller_email', 'buyer_email', 'type', 'charge_payer']);
        $currency = Currency::find($request->input('currency_id'));
        $conversion_rate =  $currency?->conversion_rate ?? 1;
        $charge = ChargeSetting::calculateEscrowCharge(convertAmount($request->input('amount'), $conversion_rate));

        Escrow::create(array_merge($input, [
            'charge_will_pay' => $request->input('charge_payer'),
            'buyer_id' => $request->input('type') == 'buy' ? auth()->user()->id : $userId,
            'seller_id' => $request->input('type') == 'buy' ? $userId : auth()->user()->id,
            'charge' => $charge,
            'transaction_type' => $request->input('type') == 'buy' ? EscrowTransactionType::Buying : EscrowTransactionType::Selling,
            'status' => EscrowStatusEnum::Pending,
            'currency_id' => $request->input('currency_id') == setting('site_currency', 'global') ? null : $request->input('currency_id'),
        ]));

        notify()->success(__('Escrow created successfully.'));

        return redirect()->route('user.escrow.index');
    }

    public function chatStore(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());
            return redirect()->back();
        }


        $escrow = Escrow::findOrFail(decrypt($request->input('escrow_id')));

        $escrow->chats()->create([
            'seller_id' => auth()->user()->id == $escrow->seller_id ? auth()->user()->id : null,
            'buyer_id' => auth()->user()->id == $escrow->buyer_id ? auth()->user()->id : null,
            'message' => $request->input('message'),
        ]);

        notify()->success(__('Chat sent successfully.'));

        return redirect()->back();
    }

    public function show($id)
    {
        $user = auth()->user();
        $escrow = Escrow::with('category', 'milestones', 'chats.buyer', 'chats.seller')->findOrFail(decrypt($id));
        $gateways = DepositMethod::query()
            ->where('status', 1)
            ->get();
        return view('frontend::user.escrow.show', compact('escrow', 'gateways', 'user'));
    }

    public function milestoneStore(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'amount' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());
            return redirect()->back();
        }

        $escrow = Escrow::findOrFail($request->input('escrow_id'));

        if ($request->input('amount') > $escrow->buyerPayableAmount() - $escrow->milestones->where('status', MilestoneStatusEnum::Paid)->sum('amount')) {
            notify()->error(__('Amount is greater than remaining amount.'));
            return redirect()->back();
        }

        $escrow->milestones()->create([
            'title' => $request->input('title'),
            'amount' => $request->input('amount'),
            'description' => $request->input('description'),
            'status' => MilestoneStatusEnum::Unpaid,
        ]);

        notify()->success(__('Milestone created successfully.'));

        return redirect()->back();
    }

    public function statusUpdate(Request $request, $status)
    {
        $escrow = Escrow::findOrFail($request->input('escrow_id'));
        $statusEnum = EscrowStatusEnum::tryFrom($status);
        $data = ['status' => $statusEnum];
        if ($statusEnum == EscrowStatusEnum::Disputed) {
            $request->validate([
                'disputed_reason' => 'required',
            ]);

            $data += [
                'disputed_by' => $request->input('disputed_by'),
                'disputed_reason' => $request->input('disputed_reason'),
            ];
        }
        $escrow->update($data);
        notify()->success(__('Escrow status updated successfully.'));
        return redirect()->back();
    }

    public function payment(ManualGatewayRequest $request)
    {
        $milestone = Milestone::findOrFail($request->get('id'));
        $gatewayInfo = DepositMethod::find($request->gateway_id);
        $sellerReceivedAmount = Transaction::where('user_id', $milestone->escrow->seller_id)->where('escrow_id', $milestone->escrow->escrow_no)->where('status', TxnStatus::Success)->sum('amount');
        $buyerPaidAmount = $sellerReceivedAmount;
        $amount = convertAmount($request->amount, $milestone->escrow->conversion_rate);

        $sellerAmount = $amount;
        $sellerReceivedAmount = convertAmount($sellerReceivedAmount, $milestone->escrow->conversion_rate);

        $charge = 0;

        if ($gatewayInfo) {
            $charge = $gatewayInfo->charge_type == 'percentage' ? ($gatewayInfo->charge / 100) * $amount : $gatewayInfo->charge;
        }

        $finalAmount = $amount + $charge;
        $senderUser = auth()->user();
        $receivedUser = $milestone->escrow->seller;


        //get milestone position
        $milestones = $milestone->escrow->milestones()->orderBy('created_at')->get();
        $position = $milestones->search(function ($item) use ($milestone) {
            return $item->id === $milestone->id;
        });

        $mile_stone_number = $position + 1;


        if ($request->input('payment_type') == 'wallet' && $senderUser->balance < $finalAmount) {
            notify()->error(__('Insufficient balance.'));
            return redirect()->back();
        }

        try {

            DB::beginTransaction();

            $senderTxnInfo = Transaction::create([
                'amount' => $amount,
                'charge' => $charge,
                'final_amount' => $finalAmount,
                'method' => 'System',
                'description' => '#' . $mile_stone_number . ' Milestone Payment Sent Of #' . $milestone->escrow->escrow_no,
                'type' => TxnType::SendMoney,
                'status' => TxnStatus::Success,
                'user_id' => $senderUser->id,
                'escrow_id' => $milestone->escrow->escrow_no,
                'from_model' => 'User',
                'pay_currency' => $milestone->escrow->currency?->code ?? setting('site_currency', 'global'),
            ]);


            if ($sellerAmount > 0) {
                $receivedTxnInfo = Transaction::create([
                    'amount' => $sellerAmount,
                    'charge' => 0,
                    'final_amount' => $sellerAmount,
                    'method' => 'System',
                    'description' => '#' . $mile_stone_number . ' Milestone Payment Received Of #' . $milestone->escrow->escrow_no,
                    'type' => TxnType::ReceiveMoney,
                    'status' => TxnStatus::Success,
                    'user_id' => $receivedUser->id,
                    'from_user_id' => $senderUser->id,
                    'escrow_id' => $milestone->escrow->escrow_no,
                    'from_model' => 'User',
                    'pay_currency' => $milestone->escrow->currency?->code ?? setting('site_currency', 'global'),
                ]);

                $receivedUser->balance += $sellerAmount;
                $receivedUser->save();
            }

            if ($request->input('payment_type') == 'wallet') {
                $senderUser->balance -= $finalAmount;
                $senderUser->save();
            }

            if ($buyerPaidAmount += $amount == convertAmount($milestone->escrow->buyerPayableAmount(), $milestone->escrow->conversion_rate)) {
                $milestone->escrow->update(['status' => EscrowStatusEnum::Released]);
            }

            $milestone->update(['status' => MilestoneStatusEnum::Paid]);

            $shortcodes = [
                '[[full_name]]' => $senderUser->full_name,
                '[[amount]]' => number_format($senderTxnInfo->amount, setting('site_currency_decimals', 'global')),
                '[[currency]]' =>  setting('site_currency', 'global'),
                '[[sender_name]]' => $senderUser->full_name,
                '[[sender_account_no]]' => $senderUser->account_number,
                '[[transaction_link]]' => route('user.escrow.show', encrypt($milestone->escrow->id)),
                '[[site_title]]' => setting('site_title', 'global'),
            ];

            $this->sendNotify($receivedUser->email, 'user_receive_money', 'User', $shortcodes, $receivedUser->phone, $receivedUser->id, route('user.escrow.show', encrypt($milestone->escrow->id)));

            if ($gatewayInfo && $gatewayInfo->type == strtolower(GatewayType::Manual->value)) {
                $value = [];
                foreach ($gatewayInfo->field_options as $filed) {
                    if ($filed['type'] == 'file') {
                        if ($request->hasFile([strtolower(str_replace(' ', '_', $filed['name']))])) {
                            $value[strtolower(str_replace(' ', '_', $filed['name']))] = self::imageUploadTrait($request[strtolower(str_replace(' ', '_', $filed['name']))]);
                        }
                    } else {
                        $value[strtolower(str_replace(' ', '_', $filed['name']))] = $request[strtolower(str_replace(' ', '_', $filed['name']))];
                    }
                }

                $manual_field_data = count($value) > 0 ? $value : null;
                $senderTxnInfo->update(['manual_field_data' => $manual_field_data, 'status' => TxnStatus::Pending]);
                $receivedTxnInfo->update(['manual_field_data' => $manual_field_data, 'status' => TxnStatus::Pending]);
            }

            $notify = [
                'card-header' => 'Milestone Payment',
                'title' => formatAmount($senderTxnInfo->amount, $senderTxnInfo->currency, true) . ' Milestone Payment Sent Successfully',
                'tnx_id' => $senderTxnInfo->tnx,
                'amount' => formatAmount($senderTxnInfo->amount, $senderTxnInfo->currency, true),
                'charge' => formatAmount($senderTxnInfo->charge, $senderTxnInfo->currency, true),
                'type' => $senderTxnInfo->type,
                'final_amount' => formatAmount($senderTxnInfo->final_amount, $senderTxnInfo->currency, true),
                'status' => $senderTxnInfo->status,
                'date' => $senderTxnInfo->created_at,
            ];

            if ($gatewayInfo && $gatewayInfo->type == strtolower(GatewayType::Automatic->value)) {
                return self::depositAutoGateway($gatewayInfo->gateway_code, $receivedTxnInfo);
            }

            DB::commit();

            return view('frontend::user.escrow.include.__success', ['notify' => $notify, 'escrow_id' => $milestone->escrow->id]);
        } catch (\Throwable $throwable) {

            DB::rollBack();

            notify()->error(__('Sorry! Something went wrong.'));

            return redirect()->back();
        }

        return redirect()->back();
    }

    public function gateway(Request $request)
    {
        $gateway = DepositMethod::findOrFail($request->input('gateway_id'));
        $escrow = Escrow::findOrFail($request->input('escrow_id'));
        $filed_options = $gateway->field_options;
        $payment_details = $gateway->payment_details;
        $amount = convertAmount($request->amount, $escrow->conversion_rate);
        if ($gateway->charge_type == 'percentage') {
            $charge = $gateway->charge / 100 * $amount;
        } else {
            $charge = $gateway->charge;
        }

        $payable_amount = $amount + $charge;

        $currency_symbol = $gateway->currency;

        return view('frontend::user.escrow.include.__gateway', compact('filed_options', 'payment_details', 'amount', 'charge', 'payable_amount', 'currency_symbol', 'escrow'))->render();
    }

    public function onetimePayment(ManualGatewayRequest $request, $id)
    {
        $escrow = Escrow::findOrFail($id);
        $gatewayInfo = DepositMethod::find($request->gateway_id);
        $amount = convertAmount($request->amount, $escrow->conversion_rate);
        $sellerAmount = $amount;

        $charge = 0;

        if ($gatewayInfo) {
            $charge = $gatewayInfo->charge_type == 'percentage' ? ($gatewayInfo->charge / 100) * $amount : $gatewayInfo->charge;
        }

        $finalAmount = $amount + $charge;
        $senderUser = auth()->user();
        $receivedUser = $escrow->seller;

        if ($request->input('payment_type') == 'wallet' && $senderUser->balance < $finalAmount) {
            notify()->error(__('Insufficient balance.'));
            return redirect()->back();
        }

        try {

            DB::beginTransaction();

            $senderTxnInfo = Transaction::create([
                'amount' => $amount,
                'charge' => $charge,
                'final_amount' => $finalAmount,
                'method' => 'System',
                'description' => 'Payment Sent Of #' . $escrow->escrow_no,
                'type' => TxnType::SendMoney,
                'status' => TxnStatus::Success,
                'user_id' => $senderUser->id,
                'escrow_id' => $escrow->escrow_no,
                'from_model' => 'User',
                'pay_currency' => $escrow->currency?->code ?? setting('site_currency', 'global'),
            ]);

            if ($sellerAmount > 0) {

                $receivedTxnInfo = Transaction::create([
                    'amount' => $sellerAmount,
                    'charge' => 0,
                    'final_amount' => $sellerAmount,
                    'method' => 'System',
                    'description' => 'Payment Received Of #' . $escrow->escrow_no,
                    'type' => TxnType::ReceiveMoney,
                    'status' => TxnStatus::Success,
                    'user_id' => $receivedUser->id,
                    'from_user_id' => $senderUser->id,
                    'escrow_id' => $escrow->escrow_no,
                    'from_model' => 'User',
                    'pay_currency' => $escrow->currency?->code ?? setting('site_currency', 'global'),
                ]);

                $receivedUser->balance += $sellerAmount;
                $receivedUser->save();
            }

            $senderTxnInfo->update(['escrow_id' => $escrow->escrow_no]);

            if ($request->input('payment_type') == 'wallet') {
                $senderUser->balance -= $finalAmount;
                $senderUser->save();
            }

            $escrow->update(['status' => EscrowStatusEnum::Released]);

            $shortcodes = [
                '[[full_name]]' => $senderUser->full_name,
                '[[amount]]' => number_format($senderTxnInfo->amount, setting('site_currency_decimals', 'global')),
                '[[currency]]' => setting('site_currency', 'global'),
                '[[sender_name]]' => $senderUser->full_name,
                '[[sender_account_no]]' => $senderUser->account_number,
                '[[transaction_link]]' => route('user.escrow.show', encrypt($escrow->id)),
                '[[site_title]]' => setting('site_title', 'global'),
            ];

            $this->sendNotify($receivedUser->email, 'user_receive_money', 'User', $shortcodes, $receivedUser->phone, $receivedUser->id, route('user.escrow.show', encrypt($escrow->id)));

            if ($gatewayInfo && $gatewayInfo->type == strtolower(GatewayType::Manual->value)) {
                $value = [];
                foreach ($gatewayInfo->field_options as $filed) {
                    if ($filed['validation'] == 'required') {
                        if ($request[strtolower(str_replace(' ', '_', $filed['name']))] == null) {
                            return back()->with('error', __('The ' . $filed['name'] . ' field are required'));
                        }
                    }
                    if ($filed['type'] == 'file') {
                        if ($request->hasFile([strtolower(str_replace(' ', '_', $filed['name']))])) {
                            $value[strtolower(str_replace(' ', '_', $filed['name']))] = self::imageUploadTrait($request[strtolower(str_replace(' ', '_', $filed['name']))]);
                        }
                    } else {
                        $value[strtolower(str_replace(' ', '_', $filed['name']))] = $request[strtolower(str_replace(' ', '_', $filed['name']))];
                    }
                }

                $manual_field_data = count($value) > 0 ? $value : null;
                $senderTxnInfo->update(['manual_field_data' => $manual_field_data, 'status' => TxnStatus::Pending]);
                $receivedTxnInfo->update(['manual_field_data' => $manual_field_data, 'status' => TxnStatus::Pending]);
            }

            if ($gatewayInfo && $gatewayInfo->type == strtolower(GatewayType::Automatic->value)) {
                return self::depositAutoGateway($gatewayInfo->gateway_code, $receivedTxnInfo);
            }

            $notify = [
                'card-header' => 'Milestone Payment',
                'title' => formatAmount($senderTxnInfo->amount, $senderTxnInfo->currency, true) . ' Milestone Payment Sent Successfully',
                'tnx_id' => $senderTxnInfo->tnx,
                'amount' => formatAmount($senderTxnInfo->amount, $senderTxnInfo->currency, true),
                'charge' => formatAmount($senderTxnInfo->charge, $senderTxnInfo->currency, true),
                'type' => $senderTxnInfo->type,
                'final_amount' => formatAmount($senderTxnInfo->final_amount, $senderTxnInfo->currency, true),
                'status' => $senderTxnInfo->status,
                'date' => $senderTxnInfo->created_at,
            ];

            DB::commit();

            return view('frontend::user.escrow.include.__success', ['notify' => $notify, 'escrow_id' => $id]);
        } catch (\Throwable $throwable) {

            DB::rollBack();

            notify()->error(__('Sorry! Something went wrong.'));

            return redirect()->back();
        }

        return redirect()->back();
    }
}