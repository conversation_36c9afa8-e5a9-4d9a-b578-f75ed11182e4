@extends('frontend::layouts.user')

@section('title')
    {{ __('Escrow Details') }}
@endsection

@section('header_title')
    {{ __('Escrow Details') }}
@endsection

@section('content')
    <div class="row gy-30">
        <div class="col-xxl-12">
            <div class="my-escro-details">
                <div class="row gy-30">
                    <div class="col-xxl-5 col-xl-5 col-lg-6">
                        <!-- Escrow details -->
                        <div class="escrow-details-card">
                            <div class="escrow-details-heading">
                                <h6 class="title">{{ __('Details') }}</h6>
                            </div>
                            <div class="escrow-details-inner">
                                <div class="escrow-details-list">
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Escrow ID') }}</div>
                                        <div class="escrow-details-value">#{{ $escrow->escrow_no }}</div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Title') }}</div>
                                        <div class="escrow-details-value">{{ $escrow->title }}</div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Transection') }}</div>
                                        <div class="escrow-details-value">
                                            @if ($escrow->seller_id == auth()->id())
                                                {{ __('Selling to') }} {{ $escrow->seller->full_name }}
                                            @else
                                                {{ __('Buying from') }} {{ $escrow->buyer->full_name }}
                                            @endif
                                        </div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Category') }}</div>
                                        <div class="escrow-details-value">{{ $escrow->category->name }}</div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Amount') }}</div>
                                        <div class="escrow-details-value">
                                            {{ $escrow->currency_sign }}{{ number_format($escrow->amount, $escrow->decimal) }}
                                            @if ($escrow->currency_id)
                                                ({{ defaultAmountConversion($escrow->amount, $escrow->conversion_rate) }})
                                            @endif
                                        </div>
                                    </div>
                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Charge') }}</div>
                                        <div class="escrow-details-value">
                                            {{ $escrow->currency_sign }}{{ number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true), $escrow->decimal) }}
                                            @if ($escrow->currency_id)
                                                ({{ setting('currency_symbol') }}{{ $escrow->charge }})
                                            @endif
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Charge Payer') }}</div>
                                        <div class="escrow-details-value">
                                            @if ($escrow->charge_will_pay == 1)
                                                {{ __('Buyer: ') }}
                                                {{ $escrow->currency_sign }}{{ number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true), $escrow->decimal) }}
                                                @if ($escrow->currency_id)
                                                    ({{ setting('currency_symbol') }}
                                                    {{ number_format(convertAmount($escrow->charge, $escrow->conversion_rate), $escrow->decimal) }})
                                                @endif
                                            @elseif($escrow->charge_will_pay == 2)
                                                {{ __('Seller: ') }}
                                                {{ $escrow->currency_sign }}{{ number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true), $escrow->decimal) }}
                                                @if ($escrow->currency_id)
                                                    ({{ setting('currency_symbol') }}{{ number_format($escrow->charge, $escrow->decimal) }})
                                                @endif
                                            @else
                                                {{ __('Buyer: ') }}
                                                {{ $escrow->currency_sign }}{{ number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true) / 2, $escrow->decimal) }}
                                                @if ($escrow->currency_id)
                                                    ({{ setting('currency_symbol') }}{{ number_format($escrow->charge / 2, $escrow->decimal) }})
                                                @endif
                                                <br>
                                                {{ __('Seller: ') }}
                                                {{ $escrow->currency_sign }}{{ number_format(convertAmount($escrow->charge, $escrow->conversion_rate, true) / 2, $escrow->decimal) }}
                                                @if ($escrow->currency_id)
                                                    ({{ setting('currency_symbol') }}{{ number_format($escrow->charge / 2, $escrow->decimal) }})
                                                @endif
                                            @endif
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Total Amount') }}</div>
                                        <div class="escrow-details-value">
                                            {{ $escrow->currency_sign }}{{ number_format($escrow->totalAmount(), $escrow->decimal) }}
                                            @if ($escrow->currency_id)
                                                ({{ setting('currency_symbol') }}{{ number_format(convertAmount($escrow->totalAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global')) }})
                                            @endif
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Buyer Payable Amount') }}</div>
                                        <div class="escrow-details-value">
                                            {{ $escrow->currency_sign }}{{ number_format($escrow->buyerPayableAmount(), $escrow->decimal) }}
                                            @if ($escrow->currency_id)
                                                ({{ setting('currency_symbol') }}{{ number_format(convertAmount($escrow->buyerPayableAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global')) }})
                                            @endif
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Seller Receivable Amount') }}</div>
                                        <div class="escrow-details-value">
                                            {{ $escrow->currency_sign }}{{ number_format($escrow->sellerReceivableAmount(), $escrow->decimal) }}
                                            @if ($escrow->currency_id)
                                                ({{ setting('currency_symbol') }}{{ number_format(convertAmount($escrow->sellerReceivableAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global')) }})
                                            @endif
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Funded Amount') }}</div>
                                        <div class="escrow-details-value">
                                            {{ $escrow->currency_sign }}{{ number_format($escrow->fundedAmount(), $escrow->decimal) }}
                                            @if ($escrow->currency_id)
                                                ({{ setting('currency_symbol') }}{{ number_format(convertAmount($escrow->fundedAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global')) }})
                                            @endif
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Remaining Amount') }}</div>
                                        <div class="escrow-details-value">
                                            {{ $escrow->currency_sign }}{{ number_format($escrow->remainingAmount(), $escrow->decimal) }}
                                            @if ($escrow->currency_id)
                                                ({{ setting('currency_symbol') }}{{ number_format(convertAmount($escrow->remainingAmount(), $escrow->conversion_rate), setting('site_currency_decimals', 'global')) }})
                                            @endif
                                        </div>
                                    </div>

                                    <div class="escrow-details-item">
                                        <div class="escrow-details-label">{{ __('Status') }}</div>
                                        <div class="escrow-details-value">
                                            @if ($escrow->status == \App\Enums\EscrowStatusEnum::Pending)
                                                <span class="td-badge badge-warning">{{ __('Pending') }}</span>
                                            @elseif($escrow->status == \App\Enums\EscrowStatusEnum::Approved)
                                                <span class="td-badge badge-info">{{ __('Approved') }}
                                                @elseif($escrow->status == \App\Enums\EscrowStatusEnum::Released)
                                                    <span class="td-badge badge-success">{{ __('Released') }}
                                                    @elseif($escrow->status == \App\Enums\EscrowStatusEnum::Disputed)
                                                        <span class="td-badge badge-secondary">{{ __('Disputed') }}
                                                        @else
                                                            <span class="td-badge badge-danger">{{ __('Cancelled') }}
                                            @endif
                                        </div>
                                    </div>
                                    @if ($escrow->status == \App\Enums\EscrowStatusEnum::Disputed)
                                        <div class="escrow-details-item">
                                            <div class="escrow-details-label">{{ __('Disputed By') }}</div>
                                            <div class="escrow-details-value">
                                                @if ($escrow->disputed_by == 1)
                                                    {{ $escrow->seller->full_name }}
                                                @else
                                                    {{ $escrow->buyer->full_name }}
                                                @endif
                                            </div>
                                        </div>

                                        <div class="escrow-details-item">
                                            <div class="escrow-details-label">{{ __('Disputed Reason') }}</div>
                                            <div class="escrow-details-value">{{ $escrow->disputed_reason }}</div>
                                        </div>
                                    @endif
                                </div>
                                <div class="btn-inner td-form-btns mt-30">

                                    @if ($escrow->status == \App\Enums\EscrowStatusEnum::Pending)
                                        @if (
                                            ($escrow->transaction_type == \App\Enums\EscrowTransactionType::Selling && $escrow->seller_id != $user->id) ||
                                                ($escrow->transaction_type == \App\Enums\EscrowTransactionType::Buying && $escrow->buyer_id != $user->id))
                                            <a href="javascript:void(0);"
                                                data-url="{{ route('user.escrow.status.update', [\App\Enums\EscrowStatusEnum::Approved]) }}"
                                                data-confirm_message="{{ __('Are you sure you want to approve this escrow?') }}"
                                                class="td-btn btn-secondary btn-sm approve-escrow">{{ __('Approved Escrow') }}</a>
                                        @endif
                                        <a href="javascript:void(0);"
                                            data-url="{{ route('user.escrow.status.update', [\App\Enums\EscrowStatusEnum::Cancelled]) }}"
                                            data-confirm_message="{{ __('Are you sure you want to cancelled this escrow?') }}"
                                            class="td-btn danger-btn btn-sm cancelled-escrow">{{ __('Cancelled Escrow') }}</a>
                                    @endif
                                    @if ($escrow->status == \App\Enums\EscrowStatusEnum::Approved)
                                        <a href="javascript:void(0);"
                                            class="td-btn danger-btn btn-sm disputed-escrow">{{ __('Dispute Escrow') }}</a>
                                    @endif
                                    @if (
                                        $escrow->payment_option == 1 &&
                                            $escrow->status == \App\Enums\EscrowStatusEnum::Approved &&
                                            $user->id == $escrow->buyer_id)
                                        <a href="javascript:void(0);" class="td-btn btn-secondary btn-sm"
                                            id="oneTimePayNow">{{ __('Pay Now') }}</a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-7 col-xl-7 col-lg-6">
                        <div class="escrow-details-card">
                            <div class="escrow-details-heading">
                                <h6 class="title">{{ __('Messages with') }}
                                    <span>{{ $escrow->seller_id == $user->id ? 'Buyer' : 'Seller' }}</span>
                                </h6>
                            </div>
                            <div class="support-wrapper-box">
                                <div class="support-box-inner">
                                    @foreach ($escrow->chats as $chat)
                                        <!-- User Message -->
                                        <div
                                            class="support-chat-text-item @if ($chat->seller_id == $user->id || $chat->buyer_id == $user->id) user-message @endif">
                                            @if ($chat->buyer_id)
                                                <div class="chat-text-avatar">
                                                    <div class="thumb">
                                                        <img src="{{ asset($chat->buyer->avatar_path) }}"
                                                            alt="{{ $chat->buyer->full_name }}">
                                                    </div>
                                                    @if ($chat->seller_id == $user->id || $chat->buyer_id == $user->id)
                                                        <div class="contents">
                                                            <h6 class="title">{{ $chat->buyer->full_name }}</h6>
                                                            <span class="designation">{{ $chat->buyer->email }}</span>
                                                        </div>
                                                    @endif
                                                </div>
                                            @else
                                                <div class="chat-text-avatar">
                                                    <div class="thumb">
                                                        <img src="{{ asset($chat->seller->avatar_path) }}"
                                                            alt="{{ $chat->seller->full_name }}">
                                                    </div>
                                                    @if ($chat->seller_id == $user->id || $chat->buyer_id == $user->id)
                                                        <div class="contents">
                                                            <h6 class="title">{{ $chat->seller->full_name }}</h6>
                                                            <span class="designation">{{ $chat->seller->email }}</span>
                                                        </div>
                                                    @endif
                                                </div>
                                            @endif
                                            <div class="message-list-box">
                                                <div class="message-list">
                                                    <p class="description">{{ $chat->message }}
                                                    </p>
                                                    <span class="timestamp">{{ $chat->created_at->diffForHumans() }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <!-- support input box -->
                                <div class="support-input-box">
                                    <form action="{{ route('user.escrow.chat.store') }}" method="POST"
                                        enctype="multipart/form-data">
                                        @csrf
                                        <input type="hidden" name="escrow_id" value="{{ encrypt($escrow->id) }}">
                                        <textarea class="form-control" name="message" placeholder="Enter your message"></textarea>
                                        <div class="btn-inner d-flex justify-content-end">
                                            <button type="submit" class="td-btn mt-30">
                                                <span class="btn-text">{{ __('Send Message') }}</span>
                                                <span class="btn-icon">
                                                    <svg width="16" height="14" viewBox="0 0 16 14"
                                                        fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M7.07513 7.0005H3.17881M3.11884 7.20701L1.46472 12.1481C1.33477 12.5363 1.2698 12.7303 1.31643 12.8499C1.35692 12.9537 1.44389 13.0324 1.5512 13.0623C1.67478 13.0968 1.86142 13.0128 2.23472 12.8448L14.0735 7.51737C14.4379 7.35337 14.6201 7.2714 14.6764 7.15749C14.7254 7.05859 14.7254 6.94248 14.6764 6.84351C14.6201 6.72967 14.4379 6.64763 14.0735 6.4837L2.23059 1.1544C1.85842 0.98692 1.67234 0.903184 1.54889 0.937535C1.44168 0.967367 1.35471 1.04585 1.31408 1.14945C1.26729 1.26875 1.33157 1.46242 1.46013 1.84975L3.1193 6.84861C3.14138 6.91513 3.15242 6.94836 3.15678 6.98236C3.16065 7.01261 3.16061 7.04315 3.15666 7.07333C3.15222 7.10733 3.14109 7.14055 3.11884 7.20701Z"
                                                            stroke="white" stroke-width="1.2" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                    </svg>
                                                </span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @if ($escrow->payment_option == 2)
                        <div class="col-xxl-12">
                            <div class="default-table-wrapper-area">
                                <div class="card-heading">
                                    <h6 class="title">{{ __('Milestones') }}</h6>
                                    @if ($escrow->status == \App\Enums\EscrowStatusEnum::Approved && $user->id == $escrow->buyer_id)
                                        <div class="link-inner">
                                            <a class="td-btn btn-h-36" href="javascript:void(0);" data-bs-toggle="modal"
                                                data-bs-target="#addMilestoneModal">
                                                <span class="btn-icon"> <i class="fi fi-br-plus"></i></span>
                                                <span class="btn-text">{{ __('Add New Milestone') }}</span>
                                            </a>
                                        </div>
                                    @endif
                                </div>
                                <!-- Recent history table -->
                                <div class="default-table-wrapper table-responsive">
                                    <table class="td-table">
                                        <thead>
                                            <tr>
                                                <th>{{ __('Title') }}</th>
                                                <th>{{ __('Description') }}</th>
                                                <th>{{ __('Amount') }}</th>
                                                <th>{{ __('Status') }}</th>
                                                @if ($escrow->status == \App\Enums\EscrowStatusEnum::Approved && $user->id == $escrow->buyer_id)
                                                    <th>{{ __('Action') }}</th>
                                                @endif
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($escrow->milestones as $milestone)
                                                <tr>
                                                    <td>
                                                        <span class="fw-7">{{ $milestone->title }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="fw-7">{{ $milestone->description }}</span>
                                                    </td>
                                                    <td>
                                                        <span
                                                            class="fw-7">{{ $escrow->currency_sign }}{{ number_format($milestone->amount, setting('site_currency_decimals', 'global')) }}</span>
                                                    </td>
                                                    <td>
                                                        @if ($milestone->status == \App\Enums\MilestoneStatusEnum::Unpaid)
                                                            <span
                                                                class="td-badge badge-warning">{{ __('Unpaid') }}</span>
                                                        @elseif($milestone->status == \App\Enums\MilestoneStatusEnum::Paid)
                                                            <span class="td-badge badge-success">{{ __('Paid') }}
                                                            @else
                                                                <span class="td-badge badge-danger">{{ __('Disputed') }}
                                                        @endif
                                                    </td>
                                                    @if (
                                                        $escrow->status == \App\Enums\EscrowStatusEnum::Approved &&
                                                            $user->id == $escrow->buyer_id &&
                                                            $milestone->status == \App\Enums\MilestoneStatusEnum::Unpaid)
                                                        <td>
                                                            <a href="javascript:void(0)"
                                                                class="td-btn btn-gray btn-h-36 pay-now"
                                                                data-milestone_id="{{ $milestone->id }}"
                                                                data-amount="{{ $milestone->amount }}">
                                                                <i class="icon-eye"></i>
                                                                {{ __('Pay') }}
                                                            </a>
                                                        </td>
                                                    @endif
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="9" class="text-center">
                                                        {{ __('No milestones found') }}</td>
                                                </tr>
                                            @endforelse
                                            <!-- Add more rows as necessary -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    {{-- add new milestone modal --}}
    @include('frontend::user.escrow.include.__add_new_milestone_modal')
    @include('frontend::user.escrow.include.__confirm_modal')
    @include('frontend::user.escrow.include.__pay_now_modal')
    @include('frontend::user.escrow.include.__onetime_paynow')
    @include('frontend::user.escrow.include.__disputed_modal')


@endsection
@push('js')
    <script>
        $(document).on('change', 'input[type="file"]', function(event) {
            console.log($(this).next('label'));
            var $file = $(this),
                $label = $file.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            var fileName = $file.val().split('\\').pop(),
                file = event.target.files[0],
                fileType = file ? file.type.split('/')[0] : null,
                tmppath = file ? URL.createObjectURL(file) : null;

            if (fileName) {
                if (fileType === "image") {
                    $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
                } else {
                    $label.addClass('file-ok').css('background-image', 'none');
                }
                $labelText.text(fileName);
                $typeFileText.hide();
                $label.siblings('.file-upload-close').show();
            } else {
                resetUpload($file, $label, $labelText, $typeFileText,
                    labelDefault);
            }
        });

        $(document).on('click', '.file-upload-close', function() {
            var $button = $(this),
                $uploadWrapper = $button.closest('.upload-custom-file'),
                $fileInput = $uploadWrapper.find('input[type="file"]'),
                $label = $fileInput.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
        });

        function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
            $fileInput.val('');
            $label.removeClass('file-ok').css('background-image', 'none');
            $labelText.text(labelDefault);
            $typeFileText.show();
            $label.siblings('.file-upload-close').hide();
        }

        $(document).ready(function() {
            $('.support-box-inner').animate({
                scrollTop: $('.support-box-inner')[0].scrollHeight
            }, 500);

            $('.approve-escrow, .cancelled-escrow').on('click', function() {
                $('#confirmModal').modal('show');
                $('#confirmForm').attr('action', $(this).data('url'));
                $('.confirm-text').text($(this).data('confirm_message'));
            });

            $('.pay-now').on('click', function() {
                $('#payNowModal').modal('show');
                $('#payAmount').val($(this).data('amount'));
                $('#milestoneId').val($(this).data('milestone_id'));
            });

            $('#oneTimePayNow').on('click', function() {
                $('#oneTimePayNowModal').modal('show');
            });

            $('.disputed-escrow').on('click', function() {
                $('#disputedModal').modal('show');
            });

            $('.payment_type').on('change', function() {
                if ($(this).val() == 'direct') {
                    $('.direct_payment').removeClass('d-none');

                } else {
                    $('.direct_payment').addClass('d-none');
                }
            });

            if ($('.support-box-inner').height() > 434) {
                $('.support-box-inner').addClass('has-scrollbar');
            }

            $(".gateway").on('change', function() {
                let gateway_id = $(this).val();
                let that = $(this);
                $.ajax({
                    url: "{{ route('user.escrow.gateway') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        gateway_id: gateway_id,
                        amount: that.parents('form').find('.pay-amount').val(),
                        escrow_id: '{{ $escrow->id }}',
                    },
                    success: function(data) {
                        if (data) {
                            $('.put_gateway_info').html(data);
                        }
                    }
                })
            });
        });
    </script>
@endpush
