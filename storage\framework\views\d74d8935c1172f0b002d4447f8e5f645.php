<!-- kyc <PERSON> -->
<div class="modal fade" id="payNowModal" tabindex="-1" aria-labelledby="payNowModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-flex justify-content-between gap-1">
                <h1 class="modal-title fs-5" id="payNowModalLabel"><?php echo e(__('Pay Now')); ?></h1>
                <button type="button" class="modal-btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fi fi-br-circle-xmark"></i>
                </button>
            </div>
            <div class="modal-body">
                <form action="<?php echo e(route('user.escrow.payment')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="id" value="" id="milestoneId">
                    <input type="hidden" name="amount" class="pay-amount" id="payAmount">
                    <div class="row gy-24">
                        <div class="col-lg-12">
                            <div class="td-form-group">
                                <label class="input-label"><?php echo e(__('Select Payment Method')); ?><span>*</span></label>
                                <div class="input-field">
                                    <select class="defaultselect2 form-select payment_type" name="payment_type">
                                        <option value=""><?php echo e(__('Select Payment Method')); ?></option>
                                        <option value="wallet">
                                            <?php echo e(__('Wallet')); ?>

                                            (<?php echo e(setting('currency_symbol')); ?><?php echo e(number_format(auth()->user()->balance, 2)); ?>)
                                        </option>
                                        <option value="direct">
                                            <?php echo e(__('Direct Payment')); ?>

                                        </option>
                                    </select>
                                </div>
                                <?php $__errorArgs = ['payment_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="feedback-invalid"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-lg-12 direct_payment d-none">
                            <div class="td-form-group">
                                <label class="input-label"><?php echo e(__('Select Gateway')); ?><span>*</span></label>
                                <div class="input-field">
                                    <select class="defaultselect2 form-select gateway" name="gateway_id">
                                        <option value=""><?php echo e(__('Select Gateway')); ?></option>
                                        <?php $__currentLoopData = $gateways; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gateway): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($gateway->id); ?>">
                                                <?php echo e($gateway->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <?php $__errorArgs = ['gateway_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="feedback-invalid"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-lg-12 direct_payment d-none">
                            <div class="put_gateway_info">

                            </div>
                        </div>
                    </div>
                    <div class="td-form-btns d-flex justify-content-end mt-30">
                        <button type="submit" class="td-btn btn-secondary"><?php echo e(__('Pay')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\escrow\app\Providers/../../resources/views/frontend/default/user/escrow/include/__pay_now_modal.blade.php ENDPATH**/ ?>