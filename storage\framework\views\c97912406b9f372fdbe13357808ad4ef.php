

<?php $__env->startSection('title'); ?>
    <?php echo e(__('Add Beneficiary')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('header_title'); ?>
    <?php echo e(__('Add Beneficiary')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    <div class="row gy-30">
        <div class="col-xxl-12">
            <!-- Withdraw history area start -->
            <div class="withdraw-history-area">
                
                <div class="withdraw-history-table-area">
                    <!-- withdraw history table -->
                    <div class="default-table-wrapper  table-responsive">
                        <table class="td-table withdraw-history-table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Name')); ?></th>
                                    <th><?php echo e(__('Account Number')); ?></th>
                                    <th><?php echo e(__('Bank Name')); ?></th>
                                    <th><?php echo e(__('Branch Name')); ?></th>
                                    <th><?php echo e(__('Routing Number')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if($beneficiaries->count() > 0): ?>
                                    <?php $__currentLoopData = $beneficiaries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $beneficiary): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <span class="fw-7"><?php echo e($beneficiary->name); ?></span>
                                            </td>
                                            <td>
                                                <span class="fw-7"><?php echo e($beneficiary->account_number); ?></span>
                                            </td>
                                            <td>
                                                <span class="fw-7">
                                                    <?php echo e($beneficiary->bank_name); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-green fw-7"><?php echo e($beneficiary->branch_name); ?></span>
                                            </td>
                                            <td>
                                                <span
                                                    class="fw-7 danger-text"><?php echo e($beneficiary->routing_number); ?></span>
                                            </td>
                                            <td>
                                                <?php if($beneficiary->status == \App\Enums\TxnStatus::Success): ?>
                                                    <span class="td-badge badge-success">
                                                        <?php echo e($beneficiary->status); ?>

                                                    </span>
                                                <?php elseif($beneficiary->status == \App\Enums\TxnStatus::Pending): ?>
                                                    <span class="td-badge badge-warning">
                                                        <?php echo e($beneficiary->status); ?>

                                                    </span>
                                                <?php elseif($beneficiary->status == \App\Enums\TxnStatus::Failed): ?>
                                                    <span class="td-badge badge-danger">
                                                        <?php echo e($beneficiary->status); ?>

                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="10" class="text-center"><?php echo e(__('No Beneficiary Found.')); ?></td>
                                    </tr>
                                <?php endif; ?>

                                <!-- Add more rows as necessary -->
                            </tbody>
                        </table>
                    </div>
                    <!-- if you need pagination add it here -->
                    <?php echo e($beneficiaries->links('frontend::user._include._pagination')); ?>

                </div>
            </div>
            <!-- Withdraw history area end -->
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\escrow\app\Providers/../../resources/views/frontend/default/user/beneficiary/index.blade.php ENDPATH**/ ?>