{"__meta": {"id": "01K0TX4J401JTF0PKS171VHWCQ", "datetime": "2025-07-23 11:47:10", "utime": **********.337442, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.497246, "end": **********.337454, "duration": 0.8402080535888672, "duration_str": "840ms", "measures": [{"label": "Booting", "start": **********.497246, "relative_start": 0, "end": **********.706685, "relative_end": **********.706685, "duration": 0.*****************, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.706693, "relative_start": 0.*****************, "end": **********.337457, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "631ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.719272, "relative_start": 0.*****************, "end": **********.721394, "relative_end": **********.721394, "duration": 0.002122163772583008, "duration_str": "2.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.335009, "relative_start": 0.****************, "end": **********.335371, "relative_end": **********.335371, "duration": 0.0003619194030761719, "duration_str": "362μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "escrow.test/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009819999999999999, "accumulated_duration_str": "9.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO' limit 1", "type": "query", "params": [], "bindings": ["g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.726483, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "escrow", "explain": null, "start_percent": 0, "width_percent": 4.684}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.7315772, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "helpers.php:371", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\escrow\\app\\helpers.php", "line": 371}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2Fhelpers.php:371", "ajax": false, "filename": "helpers.php", "line": "371"}, "connection": "escrow", "explain": null, "start_percent": 4.684, "width_percent": 4.481}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 414}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 411}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 22, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 46}], "start": **********.753622, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:138", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:138", "ajax": false, "filename": "EloquentUserProvider.php", "line": "138"}, "connection": "escrow", "explain": null, "start_percent": 9.165, "width_percent": 5.601}, {"sql": "delete from `sessions` where `id` = 'g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO'", "type": "query", "params": [], "bindings": ["g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 608}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 578}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 549}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 422}], "start": **********.941447, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:268", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:268", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "268"}, "connection": "escrow", "explain": null, "start_percent": 14.766, "width_percent": 34.42}, {"sql": "insert into `login_activities` (`user_id`, `ip`, `location`, `agent`, `updated_at`, `created_at`) values (2, '127.0.0.1', 'Bangladesh', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-23 11:47:10', '2025-07-23 11:47:10')", "type": "query", "params": [], "bindings": [2, "127.0.0.1", "Bangladesh", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "2025-07-23 11:47:10", "2025-07-23 11:47:10"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/LoginActivities.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\LoginActivities.php", "line": 28}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\escrow\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\escrow\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.324472, "duration": 0.0049900000000000005, "duration_str": "4.99ms", "memory": 0, "memory_str": null, "filename": "LoginActivities.php:28", "source": {"index": 15, "namespace": null, "name": "app/Models/LoginActivities.php", "file": "C:\\laragon\\www\\escrow\\app\\Models\\LoginActivities.php", "line": 28}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FLoginActivities.php:28", "ajax": false, "filename": "LoginActivities.php", "line": "28"}, "connection": "escrow", "explain": null, "start_percent": 49.185, "width_percent": 50.815}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://escrow.test/login", "action_name": null, "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "uri": "POST login", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fescrow%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:23-39</a>", "middleware": "web, guest", "duration": "841ms", "peak_memory": "32MB", "response": "Redirect to http://escrow.test/user/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-742464035 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-742464035\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1980961659 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2ASmihdvKJUP4WCBo0uGyP1D1JQmsu3M6kNHpwa7</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"25 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980961659\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1530055948 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"736 characters\">cookieConsent=accepted; XSRF-TOKEN=eyJpdiI6InpsZFd5UFlFNmJxUmVIYXFRNi9lNXc9PSIsInZhbHVlIjoieXFINURBYWNoNnRZNkJqM0hYdmNjVlRlckZkbGkvQXFhU2ZqQXNHbDg3OFlzK213emcrSWRDcGUrcERHMkdCa3J5SGZJcXRQdXNEM3l2UFZoRjdWb0lGWkN6TmNRNzNJZVprSjVybTNFYTdaSEVFVFI1QXBQcmF6M3FIengzZlAiLCJtYWMiOiI5MTg3MjYzNjdkMjdkZjhhZTViM2FkZmU0ZmRhYjc5YTVmZjg3MjRiMjNhMjg3YjEwNjY1ZDVkMDkxYjQ4ZGZlIiwidGFnIjoiIn0%3D; escrow_session=eyJpdiI6IjJYTVk2eHlZM3NlcVpxMFdsamhoaFE9PSIsInZhbHVlIjoiWkRCTHMxbGJkSGRWNEs2R05HcnBKYktiWTRScStpdnJwYWhiL2RnL3pIRTFvM1NsMFJ1cXNMQVhzc3JzRnhod2UzZ3dJTU4yQlhXUEg1eWoyOHhwbXFaMjcyQzhCWHhGdlFaQ0V4V3VOZFRSVGVCd0RMd01KTWZzMzJLbE1PMk8iLCJtYWMiOiI4ZDdlOTI0OTEyNjI1ODRmNjcwODE0MGQ3ZDBjYTg2ZGE1MThlOTA0NjNhZDI5YzEzM2U5OWI0NzllYWNhN2FjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://escrow.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">http://escrow.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">99</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">escrow.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530055948\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-682380267 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookieConsent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2ASmihdvKJUP4WCBo0uGyP1D1JQmsu3M6kNHpwa7</span>\"\n  \"<span class=sf-dump-key>escrow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g0SfKdkue3zZSFYUWiAbc6AtcdT2uOuLUw0GqLwO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682380267\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-712510777 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 05:47:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://escrow.test/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712510777\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1021783858 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S4lpC2qY65tzt3qwUiIwQXoo7D146MP6azJ462xT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://escrow.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>deposit_tnx</span>\" => \"<span class=sf-dump-str title=\"13 characters\">TRX4I9YBOFPS6</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021783858\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://escrow.test/login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store"}, "badge": "302 Found"}}