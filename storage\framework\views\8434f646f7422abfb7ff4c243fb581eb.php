<?php $__env->startSection('title'); ?>
    <?php echo e(match (request('type')) {
        'personal' => __('Account Settings'),
        'password' => __('Password Settings'),
        'kyc' => __('KYC'),
        'security' => __('Security'),
        default => __('Account Settings'),
    }); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('header_title'); ?>
    <?php echo e(__('Settings')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row gy-30">
        <div class="col-xxl-12">
            <!-- User setting area start -->
            <div class="user-setting-area">

                <?php echo $__env->make('frontend::user.settings.include.__setting_menu_list', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <?php echo $__env->renderWhen(
                    !request()->has('type') || request('type') == 'personal',
                    'frontend::user.settings.include.__personal',
                    [
                        'data' => $user,
                    ]
                , array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1])); ?>
                <?php echo $__env->renderWhen(request('type') == 'password', 'frontend::user.settings.include.__password', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1])); ?>
                <?php echo $__env->renderWhen(setting('kyc_verification', 'permission') && request('type') == 'kyc',
                    'frontend::user.settings.include.__kyc', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1])); ?>
                <?php echo $__env->renderWhen(setting('fa_verification', 'permission') && request('type') == 'security',
                    'frontend::user.settings.include.__security', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1])); ?>


            </div>
            <!-- User setting area end -->
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        "use strict";
        $(document).on('change', 'input[type="file"]', function(event) {
            var $file = $(this),
                $label = $file.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            var fileName = $file.val().split('\\').pop(),
                file = event.target.files[0],
                fileType = file ? file.type.split('/')[0] : null,
                tmppath = file ? URL.createObjectURL(file) : null;

            if (fileName) {
                if (fileType === "image") {

                    $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
                } else {

                    $label.addClass('file-ok').css('background-image', 'none');
                }
                $labelText.text(fileName);
                $typeFileText.hide();
                $label.siblings('.file-upload-close').show();
            } else {
                resetUpload($file, $label, $labelText, $typeFileText,
                    labelDefault);
            }
        });

        $(document).on('click', '.file-upload-close', function() {
            var $button = $(this),
                $uploadWrapper = $button.closest('.upload-custom-file'),
                $fileInput = $uploadWrapper.find('input[type="file"]'),
                $label = $fileInput.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
        });

        function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
            $fileInput.val('');
            $label.removeClass('file-ok').css('background-image', 'none');
            $labelText.text(labelDefault);
            $typeFileText.show();
            $label.siblings('.file-upload-close').hide();
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\escrow\app\Providers/../../resources/views/frontend/default/user/settings/index.blade.php ENDPATH**/ ?>