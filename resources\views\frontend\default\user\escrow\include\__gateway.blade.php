<div class="escrow-details-card mb-3">
    <div class="escrow-details-inner">
        <div class="escrow-details-list">
            <div class="escrow-details-item">
                <div class="escrow-details-label">{{ __('Amount:') }}</div>
                <div class="escrow-details-value">
                    {{ formatAmountNumber($amount, $escrow->currency_type) }}
                    {{ $currency_symbol }}
                    @if ($escrow->currency_id)
                        ({{ $escrow->currency_sign }}{{ number_format(convertAmount($amount, $escrow->conversion_rate, true), $escrow->decimal) }})
                    @endif
                </div>
            </div>
            <div class="escrow-details-item">
                <div class="escrow-details-label">{{ __('Charge:') }}</div>
                <div class="escrow-details-value">
                    {{ formatAmountNumber($charge, $escrow->currency_type) }}
                    {{ $currency_symbol }}
                    @if ($escrow->currency_id)
                        ({{ $escrow->currency_sign }}{{ number_format(convertAmount($charge, $escrow->conversion_rate, true), $escrow->decimal) }})
                    @endif
                </div>
            </div>
            <div class="escrow-details-item">
                <div class="escrow-details-label">{{ __('Payable Amount:') }}</div>
                <div class="escrow-details-value">
                    {{ formatAmountNumber($payable_amount, $escrow->currency_type) }}
                    {{ $currency_symbol }}
                    @if ($escrow->currency_id)
                        ({{ $escrow->currency_sign }}{{ number_format(convertAmount($payable_amount, $escrow->conversion_rate, true), $escrow->decimal) }})
                    @endif
                </div>
            </div>

        </div>
    </div>
</div>

<div class="row gy-24">
    @if ($filed_options)
        <div class="col-lg-12">
            <div class="alert alert-warning mt-3" role="alert">
                {!! $payment_details !!}
            </div>
        </div>
        @foreach ($filed_options as $option)
            @if ($option['type'] == 'file')
                <div class="col-lg-12">
                    <div class="td-form-group">
                        <label class="input-label">{{ ucfirst($option['name']) }}
                            <span>{{ $option['validation'] == 'required' ? '*' : '' }}</span></label>
                        <div class="input-field">
                            <div id="uploadItems">
                                <div class="upload-custom-file">
                                    <input type="file"
                                        name="{{ strtolower(str_replace(' ', '_', $option['name'])) }}"
                                        class="upload-input" accept=".gif, .jpg, .png, .jpeg, .webp, .pdf, .svg"
                                        id="image_two">
                                    <label for="image_two">
                                        <img class="upload-icon"
                                            src="{{ asset('frontend/default/images/icons/upload-iocn.svg') }}"
                                            alt="upload file">
                                        <span> <b>{{ __('Upload') }} {{ ucfirst($option['name']) }}</b></span>
                                    </label>
                                    <button type="button" class="file-upload-close" style="display: none;">
                                        <i><iconify-icon icon="tabler:circle-x"></iconify-icon></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @elseif ($option['type'] == 'textarea')
                <div class="col-lg-12">
                    <div class="td-form-group">
                        <label class="input-label"
                            for="{{ strtolower(str_replace(' ', '_', $option['name'])) }}">{{ ucfirst($option['name']) }}
                            <span>{{ $option['validation'] == 'required' ? '*' : '' }}</span></label>
                        <div class="input-field">
                            <textarea class="form-control" name="{{ strtolower(str_replace(' ', '_', $option['name'])) }}"
                                id="{{ strtolower(str_replace(' ', '_', $option['name'])) }}" {{ $option['validation'] }}></textarea>
                        </div>
                        @error('description')
                            <p class="feedback-invalid">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            @else
                <div class="col-lg-12">
                    <div class="td-form-group">
                        <label class="input-label"
                            for="title">{{ ucfirst($option['name']) }}<span>{{ $option['validation'] == 'required' ? '*' : '' }}</span></label>
                        <div class="input-field">
                            <input type="{{ $option['type'] }}" class="form-control" id="title"
                                name="{{ strtolower(str_replace(' ', '_', $option['name'])) }}"
                                {{ $option['validation'] }}>
                        </div>
                    </div>
                </div>
            @endif
        @endforeach
    @endif
</div>

<script>
    function fileUpload() {
        $(document).on('change', 'input[type="file"]', function(event) {
            console.log($(this).next('label'));
            var $file = $(this),
                $label = $file.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            var fileName = $file.val().split('\\').pop(),
                file = event.target.files[0],
                fileType = file ? file.type.split('/')[0] : null,
                tmppath = file ? URL.createObjectURL(file) : null;

            if (fileName) {
                if (fileType === "image") {
                    $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
                } else {
                    $label.addClass('file-ok').css('background-image', 'none');
                }
                $labelText.text(fileName);
                $typeFileText.hide();
                $label.siblings('.file-upload-close').show();
            } else {
                resetUpload($file, $label, $labelText, $typeFileText,
                    labelDefault);
            }
        });

        $(document).on('click', '.file-upload-close', function() {
            var $button = $(this),
                $uploadWrapper = $button.closest('.upload-custom-file'),
                $fileInput = $uploadWrapper.find('input[type="file"]'),
                $label = $fileInput.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
        });

        function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
            $fileInput.val('');
            $label.removeClass('file-ok').css('background-image', 'none');
            $labelText.text(labelDefault);
            $typeFileText.show();
            $label.siblings('.file-upload-close').hide();
        }
    }
</script>